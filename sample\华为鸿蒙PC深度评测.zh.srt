1
00:00:00,179 --> 00:00:07,179
相信熟悉数码圈的你已经听说了啊，
华为推出了新一代的轻薄本 MateBook Pro 和折叠屏笔记本MateBook Fold，

2
00:00:08,198 --> 00:00:09,939
就是我面前的这两位。

3
00:00:10,218 --> 00:00:13,797
它们是首批搭载了华为自家鸿蒙系统的 PC，

4
00:00:13,880 --> 00:00:16,396
不是 Windows，不是 Linux，不是安卓，

5
00:00:16,478 --> 00:00:18,359
而是鸿蒙 PC 哦。

6
00:00:18,739 --> 00:00:22,356
而且啊，它俩还使用了一颗前所未有的处理器，

7
00:00:22,439 --> 00:00:27,919
麒麟 X90，这是一颗专为 PC 打造的麒麟芯片。

8
00:00:28,260 --> 00:00:33,436
这样听起来是不是感觉这两款产品还挺有里程碑意义的呢？


9
00:00:33,520 --> 00:00:39,295
哎，今天我们就要来好好测一测这个麒麟 X90
华为的 PC 芯片，

10
00:00:39,378 --> 00:00:41,076
它到底是何方神圣？

11
00:00:41,158 --> 00:00:42,055
性能如何？

12
00:00:42,139 --> 00:00:43,076
功耗如何？

13
00:00:43,158 --> 00:00:46,195
相当于苹果和 Intel 的哪一代产品？

14
00:00:46,279 --> 00:00:52,356
以及啊，我们也要来好好看一下这个鸿蒙 PC
到底能做些什么，

15
00:00:52,439 --> 00:00:54,639
给它开发软件的体验到底怎么样。

16
00:00:54,959 --> 00:00:58,015
今天的节目干货满满，一定要给我们点赞收藏转发，

17
00:00:58,098 --> 00:00:59,418
那我们就赶紧开始吧。

18
00:00:59,939 --> 00:01:03,356
这台就是起售价七九九九的MateBook Pro啦，


19
00:01:03,439 --> 00:01:07,876
看起来这个面孔就很熟悉啊，它用的就是之前
MateBook XPro 上的模具，

20
00:01:07,959 --> 00:01:12,039
只是换了一个处理器，换了个系统，
还有一些其他的小变化。

21
00:01:12,418 --> 00:01:16,075
单从用料的角度啊，这个机器卖七九九九算是很可以的了，


22
00:01:16,159 --> 00:01:20,195
因为MateBook XPro
本来就是上万的旗舰轻薄本模具，

23
00:01:20,278 --> 00:01:23,215
九百八十克的重量已实现了四十瓦的性能释放，

24
00:01:23,299 --> 00:01:25,736
与此同时还保持了顶级的做工。

25
00:01:25,819 --> 00:01:29,896
想了解这个模具的话，推荐各位去看一下B巴的视频，

26
00:01:29,978 --> 00:01:33,715
那我们就不赘述了，直接开拆，卸下底部螺丝，

27
00:01:33,799 --> 00:01:37,438
包括藏在脚垫里的两颗，就可以把它的底壳拆下来了。

28
00:01:37,819 --> 00:01:40,316
这个就是MateBook Pro 的内部设计了，

29
00:01:40,399 --> 00:01:43,179
最显眼的还是中间的这一大块均热板。

30
00:01:43,519 --> 00:01:46,536
我们断电后卸下散热螺丝，取下这块均热板，

31
00:01:46,618 --> 00:01:48,459
就可以看到主板的真容了。

32
00:01:48,739 --> 00:01:55,215
这个机器的主板设计有点想法的啊，
它是中间一个矩形的主板加左右两侧小板的分离结构，

33
00:01:55,299 --> 00:02:00,539
接着往下拆掉风扇，再拆掉固态，这个 SSD
底下就藏着处理器的板载内存了。

34
00:02:00,998 --> 00:02:05,516
再拆掉转轴处的保护罩，你就会发现主板上大量地使用了
BTB 连接，

35
00:02:05,599 --> 00:02:09,015
顶上是 BTB 平线，左右两侧是和小板连接的排线，

36
00:02:09,098 --> 00:02:12,955
再加上底下的排线，可以说整个主板被排线作祟，

37
00:02:13,038 --> 00:02:16,718
围了一圈啊，有种万物聚拢到中间的感觉啦。

38
00:02:17,218 --> 00:02:19,936
那这块就是我们拆下来的 MateBook Pro
的主板了，

39
00:02:20,019 --> 00:02:22,479
中间这一颗啊，就是麒麟 X90 处理器。

40
00:02:22,938 --> 00:02:25,816
意料之外的作为一颗自研ARM SOC，

41
00:02:25,899 --> 00:02:32,899
它并没有像苹果 M 系列，高通 Xelite 和
Intel 的 Luna Lake 或者手机那样使用集成度和能效都更优的 PMIC 供电，

42
00:02:33,959 --> 00:02:37,959
而是继续和传统的 X86 芯片一样使用了 VRM
供电。

43
00:02:38,158 --> 00:02:40,699
你可以看到主板上的各种电感和 MOSFET。

44
00:02:40,859 --> 00:02:44,019
那我的理解可能是为了方便沿用它上一代的模具吧。

45
00:02:44,258 --> 00:02:45,975
上一代Intel的 Meteor Lake的机器啊，


46
00:02:46,058 --> 00:02:49,258
就是 VRM 供电的，希望这不会太影响到功耗吧。

47
00:02:49,679 --> 00:02:53,498
那么是时候来聊一下中间的这一颗麒麟 X90 芯片啦。


48
00:02:53,799 --> 00:02:57,816
看上它的第一眼，你就会发现它的这个核心面积非常之大，


49
00:02:57,899 --> 00:03:03,295
达到了两百零八平方毫米，这比苹果 M 系列，AMD
的七八四零H，

50
00:03:03,378 --> 00:03:05,776
高通的 Xelite 都要大得多啊，

51
00:03:05,859 --> 00:03:07,378
仅次于 HX 三七零。

52
00:03:07,739 --> 00:03:14,056
实际上相比手机的麒麟九零二零，这颗给 PC 设计的
X90 面积大了百分之四十八啊，

53
00:03:14,139 --> 00:03:16,919
这还是在 X90 没有集成五代基带的情况下。

54
00:03:17,199 --> 00:03:20,156
这么大一个SOC，它的面积到底都用在哪儿了呢？

55
00:03:20,239 --> 00:03:23,699
其实啊，主要就是 CPU，GPU 和 NPU。

56
00:03:24,038 --> 00:03:26,756
X90 的 CPU 部分由四颗超大核，

57
00:03:26,838 --> 00:03:31,216
四颗大核和两颗中核组成，每一种核心都支持超线程，

58
00:03:31,299 --> 00:03:33,498
总共十核二十线程啊。

59
00:03:33,778 --> 00:03:37,395
X90 的超大核基于九零一零的超大核改进而来，

60
00:03:37,479 --> 00:03:41,019
每核配备一兆的 L2 缓存，最高两点三二G 的频率。


61
00:03:41,318 --> 00:03:45,355
而 X90 的大核也是基于九零一零的超大核改来的，

62
00:03:45,438 --> 00:03:49,635
但是略有缩水啊，面积要稍微小一些。
L2缓存依然是一兆，

63
00:03:49,718 --> 00:03:51,299
最高两点零一G 的频率。

64
00:03:51,459 --> 00:03:56,938
所以与其说是超大核跟大核，
不如说是大一点的超大核跟小一点的超大核吧。

65
00:03:57,378 --> 00:04:02,776
两颗中核则是基于九零一零的大核，但是把
L2缓存砍到了五幺二K，

66
00:04:02,859 --> 00:04:04,058
当成小核来用了。

67
00:04:04,318 --> 00:04:07,376
整个 CPU 中间还提供了十六兆的 L3缓存，

68
00:04:07,459 --> 00:04:08,639
由所有核心共享。

69
00:04:08,859 --> 00:04:11,695
这个 CPU 规模啊算是相当庞大的了，

70
00:04:11,778 --> 00:04:13,818
只是频率依然跑得比较低啊。

71
00:04:13,979 --> 00:04:17,636
正上方是麒麟 X90 的 GPU 马良九一六，

72
00:04:17,718 --> 00:04:20,615
和九零一零上的马良九一零是同一代架构，

73
00:04:20,699 --> 00:04:24,016
但是规模大了不少，从四 CU 加到了六 CU，

74
00:04:24,098 --> 00:04:25,639
最高频率九百五十兆。

75
00:04:25,858 --> 00:04:29,915
旁边这一小块是 NPU，比手机上要大一倍的规模，

76
00:04:29,999 --> 00:04:32,259
最后还提供了八兆的 SLC。

77
00:04:32,658 --> 00:04:38,875
作为一颗 PC 芯片，麒麟 X90
理所当然地和苹果、Intel、AMD 一样支持一二八位的内存，

78
00:04:38,959 --> 00:04:41,658
同屏比手机的六十四位内存带宽要大一倍。

79
00:04:41,959 --> 00:04:46,375
具体来说，MateBook Pro 是配备了
LPDDR5六千四双通道内存，

80
00:04:46,459 --> 00:04:49,459
所以 CPU 性能应该会比同架构的手机来得更好。

81
00:04:49,978 --> 00:04:51,759
实测结果也是应证了这一点。

82
00:04:51,918 --> 00:04:56,036
这一次我们把行业标准工具 SPEC 2017
搬上了鸿蒙 PC，

83
00:04:56,119 --> 00:04:58,536
除 Fortune 以外的测项都已经可以跑通了，

84
00:04:58,619 --> 00:05:02,759
那我们就剔除以往的 Fortune
子项来对比一下这个 CPU 的单核。

85
00:05:03,298 --> 00:05:06,896
X90 的这颗超大核比同频的九零一零要再强一点，

86
00:05:06,978 --> 00:05:08,678
但不如九零二零的超大核。

87
00:05:09,178 --> 00:05:11,675
X90 的这颗大核性能要稍差一些，

88
00:05:11,759 --> 00:05:14,576
而中核由于只有五一二K的缓存性能，

89
00:05:14,658 --> 00:05:17,079
其实打不过同源的九零一零大核。

90
00:05:17,379 --> 00:05:19,319
不过当小核用是绰绰有余了。

91
00:05:19,598 --> 00:05:23,475
那么它这个超大核的单核性能放在 PC
里大概是什么水平呢？

92
00:05:23,559 --> 00:05:27,319
差不多就相当于 Intel 的
I七八五六五U的单核吧。

93
00:05:27,478 --> 00:05:29,019
Skylake 时期的单核。

94
00:05:29,379 --> 00:05:31,935
也跟苹果的 A12Z的单核接近。

95
00:05:32,019 --> 00:05:34,755
没办法嘛，两点三G 的频率实在是太低了，

96
00:05:34,838 --> 00:05:37,019
核心做得再大也很难跑出性能来啊。

97
00:05:37,399 --> 00:05:40,276
能效上说呢，鸿蒙 PC 我们就只能测主板功耗，

98
00:05:40,358 --> 00:05:44,076
测不到封装功耗啊， PC
由于外围功耗比手机要高得多，

99
00:05:44,158 --> 00:05:46,576
再加上麒麟 X90 用了 VRM 供电，

100
00:05:46,658 --> 00:05:50,819
它的板耗肯定是没法和手机比的，我们来对比一下其他的
PC 好了。

101
00:05:51,738 --> 00:05:55,456
其实吧，看单核能效的话，这个麒麟 X90
倒是还行啊，

102
00:05:55,538 --> 00:05:58,935
虽然单核性能差，但好在功耗也不高，

103
00:05:59,019 --> 00:06:03,196
和优等生苹果可能没法比，那我们可以和 AMD
前两年的 Zen四，

104
00:06:03,278 --> 00:06:07,555
四纳米 CPU 八八四0HS，也就是七八四0HS
来对比一下啊，

105
00:06:07,639 --> 00:06:12,555
正好它俩用的都是 VRM 供电，看起来 X90
平台的单核性能跟功耗呢，

106
00:06:12,639 --> 00:06:16,615
应该和三G赫兹下的八八四0HS轻薄本差不多吧，

107
00:06:16,699 --> 00:06:18,838
两者的单核能效还挺接近的。

108
00:06:19,038 --> 00:06:21,615
当然了， X86 处理器胜在能跑高频，

109
00:06:21,699 --> 00:06:25,379
满血五 G赫兹的 ZEN四，那不用讲都知道有多强了。


110
00:06:25,478 --> 00:06:32,478
但如果不求那部分爆发的负载性能，
放在轻薄本上用的话呢，X90 的单核论能效还不算太落后啊，

111
00:06:32,819 --> 00:06:36,348
就和前两年的 X86 平台差不多啊。那么俗话说啊，

112
00:06:36,430 --> 00:06:40,588
单核不够，多核来凑，虽然缺了那一部分爆发性能，

113
00:06:40,672 --> 00:06:43,471
但是至少这麒麟 X90是很愿意堆料啊。

114
00:06:43,791 --> 00:06:47,367
CPU 核心堆得足够多，再加上超线程的帮忙，

115
00:06:47,451 --> 00:06:49,827
它的多核性能到底怎么样呢？

116
00:06:49,911 --> 00:06:54,788
哎，这个多核测试我们就没办法像其他的 PC
那样测或是 Cinebench 了，

117
00:06:54,870 --> 00:06:58,767
因为这几个跑分软件都没有登录鸿蒙系统，

118
00:06:58,850 --> 00:07:03,011
并且闭源软件我们没有源码的话是没法移植过来测的。

119
00:07:03,250 --> 00:07:07,327
好在我们的工程师想办法移植了几个开源软件到鸿蒙上，

120
00:07:07,411 --> 00:07:10,771
也算是能测出一些关键的性能数据了一起来看一下。

121
00:07:11,230 --> 00:07:15,427
我们首先跑了 FFmpeg 的 X64 CPU
视频编码，

122
00:07:15,511 --> 00:07:17,670
这个主要是考验多核整数性能。

123
00:07:17,911 --> 00:07:20,870
我们根据一零八零转码的用时计算了性能倍率。

124
00:07:21,050 --> 00:07:27,007
哎，你还别说啊，这个麒麟 X90 的 CPU
多核还真不错啊，

125
00:07:27,091 --> 00:07:30,487
在四加四加二的这种疯狂的堆核测试下，

126
00:07:30,571 --> 00:07:35,608
麒麟 X90 可以打赢只有四个超大核加四个中核的
M1、M2，

127
00:07:35,690 --> 00:07:38,908
甚至是M3和 Intel 的 Luna Lake，

128
00:07:38,990 --> 00:07:41,151
是不是有点没想到啊？

129
00:07:41,331 --> 00:07:44,348
结合功耗来看，再有规模优势的情况下，

130
00:07:44,430 --> 00:07:48,870
麒麟 X90 的多核能效其实也和 Luna
Lake 差不太多。

131
00:07:49,230 --> 00:07:52,288
当然了，和 Luna Lake
比多核可能还是有点赖皮了啊，

132
00:07:52,372 --> 00:07:55,908
等于是拿 X90 的长板去打 Luna Lake
的短板了，

133
00:07:55,990 --> 00:07:56,821
对吧？

134
00:07:56,471 --> 00:08:00,367
但确实它的多核能效算是还不错的啊，

135
00:08:00,451 --> 00:08:06,350
如果是和 Intel 那边一样堆规模的
Meteor Lake 比，X90 的这个多核性能依然是有差距大。

136
00:08:06,630 --> 00:08:09,069
再来跑一个 7-ZIP 的 SDK 测试啊，

137
00:08:09,151 --> 00:08:16,151
和市面上七自带的基准测试不同，
我们编译的这个套件不仅涵盖了 LZMA 的压缩算法，

138
00:08:16,350 --> 00:08:21,968
还包含了 AES、 CRC、 SHA
等很多的加密啊哈希相关的算法测试，

139
00:08:22,050 --> 00:08:25,490
它其实是比较综合的，算是一个大的整数测试套件了。

140
00:08:25,810 --> 00:08:29,307
那我们测出来的结果，麒麟 X90 依然挺不错的，

141
00:08:29,391 --> 00:08:33,028
性能上再次打赢了苹果 M3 和 Intel
的二五八V，

142
00:08:33,110 --> 00:08:37,107
它的多核表现其实会更接近规模大一点的M1 Pro啊，


143
00:08:37,191 --> 00:08:41,307
尽管和 MateBook X Pro
的U715H还差了一截性能，

144
00:08:41,390 --> 00:08:45,171
但结合功耗的话呢，这个能效还是完全不输给
Intel 的。

145
00:08:45,571 --> 00:08:50,028
我们再来跑几个浮点测试，看看像是 Spec
Speed 里的三个多核子项，

146
00:08:50,110 --> 00:08:56,208
六幺九是比较受内存带宽制约的，麒麟 X90
在这里好像没办法访问到全部的内存带宽，

147
00:08:56,292 --> 00:08:57,692
一下子就失去优势了。

148
00:08:57,792 --> 00:09:01,408
但是六三八和六四四这两项呢， X90
的表现又挺不错的，

149
00:09:01,490 --> 00:09:04,490
性能打赢了M3，跟intel比也是不遑多让。

150
00:09:04,652 --> 00:09:07,288
如果比功耗的话，可能不像苹果那么好看吧，

151
00:09:07,371 --> 00:09:08,990
但是和 Intel 打还是有戏的。

152
00:09:09,571 --> 00:09:13,888
总体来说，这个麒麟 X90 的 CPU
单核性能比较菜，

153
00:09:13,971 --> 00:09:15,951
但是能效上是勉强合格吧。

154
00:09:16,152 --> 00:09:21,568
而多核则是给了我们不小的惊喜，
依靠堆核大法砸出了还不错的性能，

155
00:09:21,652 --> 00:09:25,350
多核能效也是完全不输给 Intel 的
Meteor Lake 和 Luna Lake。

156
00:09:25,792 --> 00:09:29,788
当然啦，硬要说的话呢，它肯定是打不过最新的
Arrow Lake-H，

157
00:09:29,871 --> 00:09:34,327
还有苹果M4的啊，
但是这个毕竟是全链路自主开发的处理器嘛，

158
00:09:34,410 --> 00:09:36,768
我本来就没有把它的期待值放得很高，

159
00:09:36,850 --> 00:09:42,248
倒不如说 X90 的 CPU
能达到现在这个性能已经远远超过很多人的预期了，

160
00:09:42,331 --> 00:09:43,347
你说是不是啊？

161
00:09:43,431 --> 00:09:45,748
这个 CPU 我觉得还是挺靠谱的，

162
00:09:45,831 --> 00:09:48,587
那这个 GPU 的性能又怎么样呢？

163
00:09:48,671 --> 00:09:50,971
哎， GPU 这玩意儿就很难测了。

164
00:09:51,231 --> 00:09:55,347
我们本来计划最好是移植一个原生的图形负载到鸿蒙系统
上，

165
00:09:55,431 --> 00:10:00,467
比如移植一个游戏，我们就先是移植了
ASOFTWARE 的经典开源游戏，

166
00:10:00,551 --> 00:10:03,748
Doom3，甚至游戏都已经能够进入到主菜单了，

167
00:10:03,831 --> 00:10:10,008
但是很可惜没能成功载入地图啊，
因为鸿蒙对于程序的占内存限制非常的严格，

168
00:10:10,091 --> 00:10:13,691
而 Doom3 恰好用了很多需要大占空间的写法。

169
00:10:14,071 --> 00:10:17,087
为了进这个主菜单，我们已经改了不少地方了，

170
00:10:17,171 --> 00:10:21,149
比如游戏原本为了性能，把大量的内存分配放在了占上，

171
00:10:21,231 --> 00:10:24,370
我们只能一个个改成堆分配，才能勉强跑起来。

172
00:10:25,130 --> 00:10:28,827
折腾了好几天啊，本来我们都已经放弃了，

173
00:10:28,910 --> 00:10:30,087
结果你猜怎么着？

174
00:10:30,171 --> 00:10:34,467
更新了一版系统，居然就能进游戏了。

175
00:10:34,551 --> 00:10:36,908
哇，这你能想得到吗？

176
00:10:36,990 --> 00:10:39,928
我感觉应该是内存管理上有过改动了，

177
00:10:40,011 --> 00:10:44,947
不过哪怕能进游戏，我们发现也没法对比测试性能，

178
00:10:45,030 --> 00:10:48,388
因为其他平台的 Doom3 啊，都是有编译器优化的，


179
00:10:48,471 --> 00:10:52,528
而我们移植的鸿蒙版一旦开了编译器优化又会直接爆掉，

180
00:10:52,611 --> 00:10:53,730
没法一直玩下去啊。

181
00:10:54,350 --> 00:10:59,671
所以尽管费了好大劲才跑起来这个游戏我们也只能放弃了。


182
00:10:59,792 --> 00:11:02,908
原生移植行不通，那还有一个办法能跑 GPU，

183
00:11:02,990 --> 00:11:07,128
就是WebGL，用浏览器来跑一些跨平台的测试。

184
00:11:07,211 --> 00:11:12,567
其实业界也有像毒蘑菇啊或者是 Aquarium
这样的特性或者 drawcall 测试，

185
00:11:12,650 --> 00:11:17,908
但是我们这次准备用一个更合理也更接近实际工况的东西
来测测试 GPU。

186
00:11:17,990 --> 00:11:21,447
而且我说出来你们绝对拍手称快。什么东西呢？

187
00:11:21,530 --> 00:11:26,327
就是 WebGL 版的 Minecraft
打上光影啊！

188
00:11:26,410 --> 00:11:30,548
首先啊， MC 的光影和大多数个光栅化游戏是接近的，


189
00:11:30,630 --> 00:11:32,788
像延迟渲染啊， SSAO， SSR 啊，

190
00:11:32,870 --> 00:11:34,788
次表面散射这些全都有啊。

191
00:11:34,870 --> 00:11:39,508
相比毒蘑菇那种通过跑三角函数只压榨硬件 SFU
单元的负载呢，

192
00:11:39,591 --> 00:11:40,931
也会更有参考性一点。

193
00:11:41,471 --> 00:11:42,847
其次啊， WebGL 这个东西呢，

194
00:11:42,931 --> 00:11:46,607
是 OpenGL ES 的子集，用
Chromium 内核的浏览器跑的话呢，

195
00:11:46,691 --> 00:11:49,467
基本上是能够直接对应到底层图形接口的，

196
00:11:49,551 --> 00:11:53,508
性能上也很接近原生，再加上它几乎是 GPU
满载运行，

197
00:11:53,591 --> 00:11:56,451
瓶颈主要在 GPU 上， CPU
的损耗影响就不大了。

198
00:11:56,931 --> 00:12:00,607
最关键的一点是在鸿蒙系统上正常的 JIT 被限制了，


199
00:12:00,691 --> 00:12:04,508
很多模拟器里的跑法都不行，但是浏览器本身的 JS
引擎啊，

200
00:12:04,591 --> 00:12:08,067
就是靠 JIT 的，所以我们用 WebGL
就相当于借壳，

201
00:12:08,150 --> 00:12:09,808
让它帮我们来完成 JIT。

202
00:12:09,892 --> 00:12:13,548
这可能是目前鸿蒙上面最接近原生性能的方式了。

203
00:12:13,630 --> 00:12:17,168
不过拿这个 WebGL 版的 MC
来评测也会有一些限制。

204
00:12:17,250 --> 00:12:24,250
首先我们没法对比高通机型，因为高通官方的 GPU
驱动为了追求性能，

205
00:12:27,110 --> 00:12:29,488
它做了很多优化工作啊，它是没有办法跑通这版 MC
的，

206
00:12:29,571 --> 00:12:32,831
这也是为什么会有社区给高通 GPU 去做
Turnip 开源驱动这种东西啊。

207
00:12:33,150 --> 00:12:40,150
其次啊，在 Windows、MacOS 和
Linux 的 Chromium 内核浏览器里面跑 WebGL 是需要经过谷歌的 Angle 转译的啊，

208
00:12:41,130 --> 00:12:42,571
这是存在性能损耗的啊。

209
00:12:42,850 --> 00:12:48,908
而安卓和鸿蒙上面就像我前面说的，
是可以直接对应到底层接口跑到 OpenGL ES 上的，

210
00:12:48,990 --> 00:12:55,528
所以真正能直接和 X90 对比 GPU
的就是手机上的麒麟和发哥的天玑平台。

211
00:12:55,610 --> 00:13:02,610
不过 PC 这段我也把转译的性能跑了一下。
我们前面在单射上看到麒麟 X 九零的这个马两九六啊，

212
00:13:02,695 --> 00:13:06,351
GPU 规模是增加到了六 CPU，
但实际到了游戏里面，

213
00:13:06,434 --> 00:13:10,595
它的性能好像并没有比九零二零的 GPU 强多少啊。

214
00:13:10,735 --> 00:13:13,831
啊这是因为它是基于九零一零上的老架构，

215
00:13:13,914 --> 00:13:16,394
软件效率差了新款 GPU 很多。

216
00:13:16,634 --> 00:13:20,392
你要知道，九零二零比九零一零的 GPU
提升是很大的，

217
00:13:20,475 --> 00:13:22,033
那么这个 X 九零的 GPU 呢，

218
00:13:22,115 --> 00:13:25,352
基本上就是和天机九千差不多的性能，

219
00:13:25,436 --> 00:13:30,174
哪怕和转移后的苹果 M一还有 Intel 的要五
H 比都差得挺远的。

220
00:13:30,436 --> 00:13:33,855
和原生的天机九千四比，这个差距就非常大了。

221
00:13:34,056 --> 00:13:37,875
应该说啊，这个 GPU 算是整个 SOC
上比较短的一块板了。

222
00:13:38,216 --> 00:13:40,751
第一代码是可以理解的，玩儿期待以后啊，

223
00:13:40,835 --> 00:13:44,812
能够用九零二零上的新架构来做他的这个 PC 的
GPU 啊。

224
00:13:44,894 --> 00:13:48,412
不过话说回来啊，这个 MC
的光影应该也是我测了这么久，

225
00:13:48,495 --> 00:13:52,452
鸿蒙玩到的第一个有点图形负载的游戏了啊，

226
00:13:52,535 --> 00:13:57,254
这个 GPU 也是不容易啊，
总算是有机会打一场酣畅淋漓的架了。

227
00:13:57,575 --> 00:14:00,072
如果要从它的应用商店里面去找游戏的话，

228
00:14:00,154 --> 00:14:03,152
那正经一点呢，可能目前就只有苍翼混沌效应、

229
00:14:03,235 --> 00:14:04,995
三国杀和微软档案了。

230
00:14:05,215 --> 00:14:07,591
那它这个 GPU 强一点，弱一点，

231
00:14:07,674 --> 00:14:10,451
在 BA 这种负载里你猜能有啥影响呢？

232
00:14:10,534 --> 00:14:15,774
一点影响都没有嘛，
还是等等未来看看能不能有更多的游戏登陆鸿蒙 PC 吧。

233
00:14:16,355 --> 00:14:18,873
好了，硬件性能能效的部分分析完了，

234
00:14:18,956 --> 00:14:20,154
干货还是挺多的吧。

235
00:14:20,475 --> 00:14:24,331
我觉得这一次麒麟 X 九零作为自研架构 PC
芯片的起点，

236
00:14:24,414 --> 00:14:29,054
应该算是挺 OK 的了，至少 CPU
比我的预期是要好不少的。

237
00:14:29,294 --> 00:14:32,113
这也得感谢芯片团队不惜成本地去堆料，

238
00:14:32,196 --> 00:14:36,075
塞了这么多核心进去，
让它的这个多核表现也算是能跟上时代了。

239
00:14:36,315 --> 00:14:39,532
不过啊，这台 PC 更让人家好奇的，

240
00:14:39,615 --> 00:14:42,152
可能还是它的这个软件部分对吧？

241
00:14:42,235 --> 00:14:45,251
鸿蒙系统啊，这个我们已经用了大半个月了，

242
00:14:45,335 --> 00:14:47,635
其中大多数时间都是在给它开发软件。

243
00:14:47,815 --> 00:14:54,312
所以今天我们就从消费者和开发者这两个角度来好好地聊
一聊鸿蒙 PC 啊。

244
00:14:54,395 --> 00:14:57,312
首先大家肯定很好奇，这个软硬件都是自己做的，

245
00:14:57,394 --> 00:14:59,731
是不是意味着续航会很好呢？

246
00:14:59,815 --> 00:15:03,072
能不能做到苹果那样优秀的办公续航呢？

247
00:15:03,154 --> 00:15:06,633
哎，我们决定基于之前的几款 PC 续航脚本的标准，

248
00:15:06,716 --> 00:15:10,552
把一些没上架的应用替换成诸如 WPS 这样的屏替，

249
00:15:10,634 --> 00:15:15,331
直接拿旧款的 Windows
和新款的鸿蒙加麒麟放在一起测一遍续航看看啊，

250
00:15:15,414 --> 00:15:16,654
结果是这个样子的。

251
00:15:17,035 --> 00:15:20,735
果然软硬协同对于这个续航的帮助确实是很大啊。

252
00:15:20,975 --> 00:15:27,452
在电池一样大的情况下，鸿蒙的 MateBook
Pro 办公续航可以坚持七小时四十三分钟，

253
00:15:27,535 --> 00:15:31,652
相比上一代 Windows 的 MateBook
X Pro 长了整整两个小时啊，

254
00:15:31,735 --> 00:15:33,434
这个表现还算是挺不错的。

255
00:15:33,815 --> 00:15:37,412
当然了，受处理器能效的限制呢，
它这个轻办公工况的续航啊，

256
00:15:37,495 --> 00:15:40,133
和十个小时以上的 Mac 还是有差距的，

257
00:15:40,216 --> 00:15:42,975
也未必打得过一些大电池的 Luna Lake 机型。


258
00:15:43,315 --> 00:15:46,851
但是对于现在想买它来尝鲜做文字办公的同学呢，

259
00:15:46,934 --> 00:15:50,095
你至少不太用着担心它频繁地没电啊。

260
00:15:50,576 --> 00:15:53,095
那鸿蒙系统还有一个优点就是流畅度。

261
00:15:53,335 --> 00:15:58,052
在手机端卷了这么多年，
鸿蒙自然是憋出了一套相当丝滑的动效啊，

262
00:15:58,134 --> 00:16:00,312
而且除非你完全满载 CPU 否则，

263
00:16:00,395 --> 00:16:06,095
你是根本见不到一点掉帧啊，
这一点显然是降维打击吃老本多年的 Windows PC 啦。

264
00:16:06,655 --> 00:16:11,692
如果哪天你路过了华为的线下店，
我是非常推荐你去感受一下它的这个系统流畅度的，

265
00:16:11,774 --> 00:16:13,274
确实是让人印象深刻啊。

266
00:16:13,615 --> 00:16:17,011
包括华为还做了很多的事情来改善它的软件性能，

267
00:16:17,095 --> 00:16:19,072
比如啊，你在QQ 里下载好文件的时候啊，

268
00:16:19,154 --> 00:16:23,493
系统就猜到了你接下来要打开，
它就已经在预加载这个文件了，

269
00:16:23,576 --> 00:16:25,634
这样打开的时候就可以更快一点。

270
00:16:25,955 --> 00:16:29,572
另外在操作逻辑上呢，鸿蒙PC没有做那种反人类的创新，


271
00:16:29,655 --> 00:16:32,471
它把很多的 Mac OS 和 Windows
的操作习惯，

272
00:16:32,554 --> 00:16:36,552
不管是快捷键、手势操作还是 UI
布局都给延续了过来，

273
00:16:36,634 --> 00:16:39,511
你从其他系统迁移过来是很容易上手的，

274
00:16:39,595 --> 00:16:41,995
而且它可以根据你的习惯来选择。

275
00:16:42,216 --> 00:16:45,692
比如啊，你既可以选择类 Windows
的这种系统托盘，

276
00:16:45,774 --> 00:16:51,152
把快捷栏放在右下角，也可以选择像这种类 Mac
OS 的菜单栏啊，

277
00:16:51,235 --> 00:16:54,932
在右上角放置这些状态栏啊，既可以还是这个味儿，

278
00:16:55,014 --> 00:16:56,552
也可以还是那个味儿。

279
00:16:56,634 --> 00:16:59,554
那有一些操作逻辑上的小细节，我觉得做得也挺好的。

280
00:16:59,735 --> 00:17:03,052
比如啊，键鼠操作和触屏操作逻辑是分开的，

281
00:17:03,134 --> 00:17:08,692
键鼠操作的时候，它是传统光标的 PC
逻辑双击打开右键子菜单，

282
00:17:08,775 --> 00:17:12,031
直接就能拖动图标，而当你触摸屏幕的时候呢，

283
00:17:12,115 --> 00:17:14,536
它就瞬间转为平板的逻辑啦。

284
00:17:14,815 --> 00:17:18,811
单击打开，长按子菜单，按住才能拖动图标，

285
00:17:18,894 --> 00:17:23,075
那这个比 Windows
十二合一那种模拟光标的触控逻辑要好太多了啊。

286
00:17:23,375 --> 00:17:29,175
那对于普通消费者来说，我觉得鸿蒙 PC
最大的缺点可能还是不自由吧。

287
00:17:29,595 --> 00:17:32,291
目前来讲，它是一个非常封闭的系统，

288
00:17:32,375 --> 00:17:36,311
无法以任何的方式执行 ELF 二进制可执行文件，

289
00:17:36,394 --> 00:17:40,451
即使已经知道它这个系统里的二进制就是 ELF 文件，


290
00:17:40,535 --> 00:17:42,892
而且 Deveco 提供的交叉编译工具链呢，

291
00:17:42,974 --> 00:17:47,172
是可以编译出可执行文件的，但就是不让你运行，

292
00:17:47,255 --> 00:17:50,932
想要运行任何的应用，你只能乖乖去商店下载，

293
00:17:51,015 --> 00:17:54,332
要不啊你就得当一回开发者，有开发者账号的情况下，

294
00:17:54,414 --> 00:17:57,315
你可以给 A J P 包签名，然后啊就可以安装了。

295
00:17:57,935 --> 00:18:03,071
但是问题来了，这个电脑它并不能给自己开发软件，

296
00:18:03,154 --> 00:18:08,392
也不能自己给包签名，因为华为的 Deveco
啊目前还没有鸿蒙版，

297
00:18:08,474 --> 00:18:10,192
只有 Mac 和 Windows 版，

298
00:18:10,275 --> 00:18:14,551
你得用其他电脑才能开发鸿蒙软件或者签名给它用啊，

299
00:18:14,634 --> 00:18:17,231
它也没有任何运行在本地的编译器啊，

300
00:18:17,315 --> 00:18:18,231
编译工具链啊。

301
00:18:18,315 --> 00:18:21,612
好在这个尴尬的问题应该很快就会有解了哦，

302
00:18:21,694 --> 00:18:25,212
不过一段时间呢，也许能见到鸿蒙上的 IDE 啦。

303
00:18:25,296 --> 00:18:30,392
对于做开发的同学来说啊，现阶段鸿蒙 PC
上也有轻量级的 IDE 能用，

304
00:18:30,474 --> 00:18:31,593
就是 Codearts。

305
00:18:31,675 --> 00:18:34,432
理论上你可以拿它做 Python 和 Java
的开发，

306
00:18:34,516 --> 00:18:38,852
但是实际用起来啊，鸿蒙版的 Codearts
还有很多功能上的缺失，

307
00:18:38,934 --> 00:18:40,432
比如它提供的 Python 环境，

308
00:18:40,516 --> 00:18:43,492
虽然有 Pip，但是一执行就会报错，

309
00:18:43,576 --> 00:18:47,212
说没有权限，虽然也可以用一些弯门邪道来执行 Pip，


310
00:18:47,296 --> 00:18:49,932
但它又装不了库，也不能自己 build WSL。

311
00:18:50,016 --> 00:18:52,593
例如 Numpy 这样的科学计算理常用库呢，

312
00:18:52,675 --> 00:18:55,454
就会因为编译不了底层加速库而安装失败。

313
00:18:55,895 --> 00:18:58,071
好消息是，如果你是拿它做 Java 开发，

314
00:18:58,154 --> 00:19:02,731
那基本环境还是有的，它已经支持了 Maven 和
Gradle 的包管理，

315
00:19:02,815 --> 00:19:06,015
安装一些使用 Spring Boot 的 Web
后端项目是没太大问题的。

316
00:19:06,654 --> 00:19:08,251
Codearts 也在它的终端里啊，

317
00:19:08,335 --> 00:19:12,051
提供了 SSH，但是目前啊，因为一些终端的 bug，


318
00:19:12,134 --> 00:19:17,951
就会导致它的 SSH
是没法连接任何有密码的服务器的啊。

319
00:19:18,035 --> 00:19:20,392
额，所以鸿蒙现在这个版本的 Codearts，
你也看到了，

320
00:19:20,474 --> 00:19:24,136
它这个可用性还有待完善啊。然后啊，

321
00:19:24,220 --> 00:19:28,635
鸿蒙 PC 其实是有终端的，但是这个终端呐，

322
00:19:28,719 --> 00:19:32,316
感觉就是个阑尾啊，它做不了任何有意义的事儿，

323
00:19:32,398 --> 00:19:34,797
不可以执行程序，不可以修改系统设置，

324
00:19:34,880 --> 00:19:37,615
甚至都不能 LS 非用户的文件目录结构，

325
00:19:37,699 --> 00:19:41,699
这就意味着你完全看不到，也摸不到任何的系统文件啊。

326
00:19:42,078 --> 00:19:46,155
与此同时啊，鸿蒙的每一个应用啊，
都有一个自己的沙箱文件环境，

327
00:19:46,239 --> 00:19:51,876
并不能够直接读取外部公开的目录，
必须通过某种方式把文件导入进来，

328
00:19:51,960 --> 00:19:54,915
而且沙箱之间也是互相隔离不互通的啊，

329
00:19:54,999 --> 00:19:57,017
所以比起 MacOS 和 Windows 呢，

330
00:19:57,099 --> 00:19:59,839
它其实会在这个方面更像 iOS 一点。

331
00:20:00,118 --> 00:20:03,256
然后你知道鸿蒙 PC 最让我难以理解的一点是什么吗？


332
00:20:03,338 --> 00:20:08,855
就是除了虚拟机以外啊，
它没有做任何主动兼容其他生态的转移层。

333
00:20:08,939 --> 00:20:11,935
啊其实你都不需要搞得多复杂啊，做一个容器，

334
00:20:12,019 --> 00:20:17,160
兼容 Linux 生态，
或者像手机的卓易通那样去兼容 Android 生态都会好很多。

335
00:20:17,499 --> 00:20:21,297
与此同时，鸿蒙 PC 也不开放
Bootloader，

336
00:20:21,380 --> 00:20:24,499
你没法给它安装 Linux 或者是 Windows
On ARM。

337
00:20:24,720 --> 00:20:28,220
你知道吗，在 PC 这个行业里，
目前还没有人敢这么干的啊。

338
00:20:28,459 --> 00:20:32,776
啊即便强势如苹果，以前也做过 Boot Camp
去兼容 Windows，

339
00:20:32,858 --> 00:20:36,056
现在呢，也允许你装 Linux 系统也支持
WINE，

340
00:20:36,138 --> 00:20:36,969
对吧？

341
00:20:36,338 --> 00:20:38,676
包括他们还做了 GPTK 之类的兼容工具，

342
00:20:38,759 --> 00:20:40,859
让你跑上 Windows 的应用和游戏。

343
00:20:41,239 --> 00:20:43,596
那说实话，我可以理解，做封闭系统呢，

344
00:20:43,679 --> 00:20:45,676
可能是为了安全可控，对吧？

345
00:20:45,759 --> 00:20:49,317
啊这个在手机上也许是行得通的，但是 PC 啊，

346
00:20:49,400 --> 00:20:52,796
作为一个吃饭用的工具，做得过于封闭了，

347
00:20:52,878 --> 00:20:56,176
那不就和带键盘的平板没啥区别了吗？

348
00:20:56,259 --> 00:21:01,155
又不开放系统权限，又不做兼容层，
甚至现成的开源软件生态，

349
00:21:01,239 --> 00:21:03,239
普通用户也没法下载来用。

350
00:21:03,440 --> 00:21:06,439
这个真的是给开发者关系部门上强度了啊。

351
00:21:06,878 --> 00:21:10,457
如果鸿蒙 PC 的目标是做一个好用的信创平台，

352
00:21:10,539 --> 00:21:12,598
哎，那我倒是觉得现在这样还挺好的。

353
00:21:13,079 --> 00:21:17,957
但凡想往更广阔的商用人群啊，还有消费级市场去拓展，

354
00:21:18,039 --> 00:21:20,798
那我觉得华为得迟早想清楚这个问题了。

355
00:21:21,298 --> 00:21:24,736
那现现在鸿蒙 PC 的商店里上架的应用还非常的有限，


356
00:21:24,818 --> 00:21:27,395
如果你买回去一定要跑一些 Windows 应用的话，


357
00:21:27,479 --> 00:21:29,939
还是有最后的一道保险的，就是虚拟机。

358
00:21:30,199 --> 00:21:33,537
我们可以在这个 OS Easy 的虚拟机里装上
Windows 十一，

359
00:21:33,619 --> 00:21:37,757
CPU 效率其实还挺高的，
但是虚拟机目前还有很多的硬伤需要解决，

360
00:21:37,839 --> 00:21:41,215
比如缺少 GPU 加速，啊所以大多数的游戏啊，

361
00:21:41,298 --> 00:21:45,516
还有很多的专业应用都没法运行，
包括外接显示器的时候呢，

362
00:21:45,598 --> 00:21:48,378
这个虚拟机的屏幕是没法投在显示器上的。

363
00:21:48,679 --> 00:21:51,355
还有啊，像折叠屏 PC 在用虚拟机的时候，

364
00:21:51,439 --> 00:21:55,598
是不能以折叠态使用的，
你得支起来横平展开才能够用得了啊。

365
00:21:55,980 --> 00:21:58,516
那消费者侧的体验姑且是这个样子的，

366
00:21:58,598 --> 00:22:01,596
那么开发者的体验又如何呢？

367
00:22:01,679 --> 00:22:04,296
是时候来聊一点真正的干货了啊！

368
00:22:04,378 --> 00:22:07,476
如果你只想做一款标准 UI，逻辑清晰、

369
00:22:07,558 --> 00:22:11,757
功能偏静态的 App，那么鸿蒙开发还是很友好的，

370
00:22:11,839 --> 00:22:13,338
甚至可以说轻松愉快啊。

371
00:22:13,598 --> 00:22:20,019
首先 TS 的开发体验确实不错，用过 Java
Script,TypeScript 的前端同学肯定会觉得挺舒服的。

372
00:22:20,298 --> 00:22:23,257
整套界面框架啊，都是 MVM 范式的，

373
00:22:23,339 --> 00:22:28,019
就跟 Swift UI 一样啊，不用自己去维护
UI 状态和逻辑状态之间的同步。

374
00:22:28,480 --> 00:22:30,679
写普通的界面程序啊，体验是很好的。

375
00:22:30,959 --> 00:22:34,056
然后 Deveco Studio 呢，是基于
JetBrains 做的，

376
00:22:34,138 --> 00:22:36,655
所以但凡 JetBrains IDE 有的功能，

377
00:22:36,739 --> 00:22:40,476
像是智能补全啊，代码重构啊，这些它也都有，

378
00:22:40,558 --> 00:22:41,878
基本没啥上手成本吧。

379
00:22:42,138 --> 00:22:46,415
那我觉得鸿蒙开发最好的一个点呢，
还是一次开发多端部署。

380
00:22:46,499 --> 00:22:51,736
写一份代码，
绝大多数情况下都可以一行不改的同时跑在手机和 PC 上，

381
00:22:51,818 --> 00:22:55,457
偶尔调用了设备不支持的函数啊，也会标注出来警告你啊。


382
00:22:55,539 --> 00:22:58,679
我觉得轻量级的开发者应该会很适应鸿蒙开发。

383
00:22:59,199 --> 00:23:04,676
但是啊，如果你更追求底层能力，
或者但凡要做一些复杂的软件，

384
00:23:04,759 --> 00:23:09,939
哎，那这个开发体验相比其他平台还是头疼了很多的啊。

385
00:23:10,419 --> 00:23:12,957
首当其冲的啊，就是因为必须要交叉编译，

386
00:23:13,039 --> 00:23:15,898
所以每次啊都需要修改各种编译脚本、

387
00:23:16,039 --> 00:23:17,056
配置文件来使用。

388
00:23:17,138 --> 00:23:20,500
华为提供的交叉编译工具链，几乎很少能开箱即用。

389
00:23:20,878 --> 00:23:24,836
其次啊，原生库目前还比较欠缺，如果你要引第三方库呢，


390
00:23:24,919 --> 00:23:28,016
绝大多数的库都没有现成支持鸿蒙的二进制包，

391
00:23:28,098 --> 00:23:31,076
基本就只能自己去找源码改源码自己编译，

392
00:23:31,159 --> 00:23:34,239
而且啊经常还要手动适配啊，魔改一堆接口。

393
00:23:34,419 --> 00:23:39,516
像 GOFW 这种非常基础的库，
虽然你能够找到有支持鸿蒙的仓库，

394
00:23:39,598 --> 00:23:45,019
但它也没有一个清晰的编译文档，
实际能不能用就全靠自己一点点试出来了。

395
00:23:45,278 --> 00:23:50,756
那某种程度上说呢，
这是一个新系统早期必然会经历的痛苦过程吧，

396
00:23:50,838 --> 00:23:52,358
所以我是可以理解的啊。

397
00:23:52,659 --> 00:23:54,996
但目前阶段啊，就连 RISC-V 啊，

398
00:23:55,078 --> 00:23:59,579
或者龙芯的 LongArch
平台库的支持度都要比鸿蒙现阶段来得更好啊。

399
00:23:59,999 --> 00:24:02,777
还有啊，一些 Posix 接口看起来似乎支持，

400
00:24:02,859 --> 00:24:04,439
但实际上又不能用。

401
00:24:04,838 --> 00:24:07,155
举个例子啊，比如 setLimit 这个 API，

402
00:24:07,239 --> 00:24:11,836
你可以写，可以调用，也不会报错，但它完全不起作用啊。


403
00:24:11,920 --> 00:24:16,875
你改了占大小，它就当你没改啊，
你不知道是你写错了还是系统本身没实现，

404
00:24:16,959 --> 00:24:18,180
啊就很让人困惑吧。

405
00:24:18,700 --> 00:24:21,855
而且鸿蒙系统还存在很多隐性的资源限制，

406
00:24:21,939 --> 00:24:25,959
例如占大小的限制啊，单 App
的内存大小限制啊等等。

407
00:24:26,239 --> 00:24:30,996
如果不踩坑，不一点点排雷呢，
正常运行代码放到上面是很可能会崩溃，

408
00:24:31,078 --> 00:24:33,159
而且你一时半会也找不到问题所在的。

409
00:24:33,439 --> 00:24:36,016
就比如我们前面移植 Dome3
的时候遇到的情况对吧？

410
00:24:36,098 --> 00:24:39,176
跑着跑着爆炸了，但没有提示站位出，

411
00:24:39,259 --> 00:24:41,756
你只会看到程序栈跑到了奇怪的地方，

412
00:24:41,838 --> 00:24:42,919
然后就挂掉了。

413
00:24:43,140 --> 00:24:46,155
啊又比如我们调试 Spec speed 的 XZ
子项的时候呢，

414
00:24:46,239 --> 00:24:51,695
需要读取一个六G的文件啊，
读着读着它就崩溃了啊后来呢才发现可能是内存限制，

415
00:24:51,778 --> 00:24:54,236
但是开发的时候呢，并没有任何的提示。

416
00:24:54,318 --> 00:24:57,615
其实像 iOS 也有这些限制，但它的限制啊，

417
00:24:57,699 --> 00:25:02,919
都有明确的文档告知开发者，
而鸿蒙这一块的文档和提示还不够完善吧。

418
00:25:03,179 --> 00:25:07,257
很多时候是你撞了南墙之后才会意识到问题所在的。
还有一点，

419
00:25:07,339 --> 00:25:11,776
我们前面有讲到过，就是鸿蒙限制了JIT。
JIT简单来说啊，

420
00:25:11,858 --> 00:25:15,576
就是程序自己在运行的过程中生成新的代码段来跑，

421
00:25:15,659 --> 00:25:18,776
它被用在很多的模拟器、解释器类的应用里，

422
00:25:18,858 --> 00:25:24,215
比如浏览器就需要在运行时通过JIT把JavaScr
ipt代码编译成原生代码，

423
00:25:24,298 --> 00:25:30,019
而模拟器就需要JIT来把游戏ROM中的主机平台上那
些指令翻译成原生指令。

424
00:25:30,179 --> 00:25:35,138
放眼业界，
哪怕在IOS上也可以申请在受限的环境下使用JIT。

425
00:25:35,338 --> 00:25:37,875
至于Windows和MacOS这种PC系统，

426
00:25:37,959 --> 00:25:42,477
更是完全不管控JIT，
所以在PC上跑带JIT的浏览器啊，

427
00:25:42,559 --> 00:25:47,058
脚本解释器，还有模拟器，
这些应该是像吃饭喝水一样简单啊。

428
00:25:47,179 --> 00:25:50,839
所以鸿蒙目前的这个政策感觉是卡的有点太紧了啊。

429
00:25:51,199 --> 00:25:55,236
反正轻松地讲呢，
鸿蒙系统目前肯定还是处于一个初级阶段吧，

430
00:25:55,318 --> 00:25:57,935
遇到什么样的问题或者是策略过于保守，

431
00:25:58,019 --> 00:26:01,036
其实都是蛮正常的，早点完善，早点起飞，

432
00:26:01,118 --> 00:26:04,180
鸿蒙生态肯定是有做大做强的潜质的。

433
00:26:04,900 --> 00:26:10,717
总的来说，MateBook
Pro作为华为的第一台从软件到硬件全部自己打造的PC，

434
00:26:10,799 --> 00:26:12,699
给我是留下了很深刻的印象啊。

435
00:26:12,980 --> 00:26:15,455
麒麟X九零我没有把它的期望值定得很高，

436
00:26:15,538 --> 00:26:19,720
但最终至少CPU多核性能能效还是挺超我的预期的。

437
00:26:19,999 --> 00:26:23,736
鸿蒙系统尽管被我们诟病太封闭了，有待放权，

438
00:26:23,818 --> 00:26:25,676
但它的优点也是非常多的。

439
00:26:25,759 --> 00:26:30,655
软硬件协同的优化也好啊，
包括流畅度上还有操作逻辑上的巧思，

440
00:26:30,739 --> 00:26:32,400
都是看得见摸得着的。

441
00:26:32,818 --> 00:26:37,997
当然啊，现阶段因为生态上的缺失，
我肯定是不会推荐一般人去买这个PC的啊，

442
00:26:38,079 --> 00:26:41,756
但如果你愿意掏着七九九九当一回小白鼠的话，

443
00:26:41,838 --> 00:26:46,336
买回去尝个鲜，体验一个全新的平台也未尝不可，

444
00:26:46,419 --> 00:26:47,249
是吧？

445
00:26:47,019 --> 00:26:49,736
不管怎么说啊，有厂商不再寄人篱下，

446
00:26:49,818 --> 00:26:54,895
而是愿意打造自己的软硬件生态啊，
我觉得总归都是值得鼓励的，

447
00:26:54,979 --> 00:26:57,955
没准未来哪一天它就真的能做成呢，对吧？

448
00:26:58,038 --> 00:27:00,395
到时候有优秀的产品问世，参与竞争，

449
00:27:00,479 --> 00:27:03,476
消费者能够多一个选择，总归是一个好事儿啊，

450
00:27:03,558 --> 00:27:06,895
是吧。那么好的今天这期鸿蒙PC的大评测，

451
00:27:06,979 --> 00:27:11,439
信息量应该已经爆炸了吧，希望大家看得足够爽啊。

452
00:27:11,638 --> 00:27:15,256
看完别忘了点赞、收藏转发支持我们，

453
00:27:15,338 --> 00:27:17,115
也别忘了关注极款这个频道。

454
00:27:17,199 --> 00:27:19,675
我是云飞，我们下次再见啦！

455
00:27:19,759 --> 00:27:20,618
拜拜！
