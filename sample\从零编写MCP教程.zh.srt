1
00:00:00,119 --> 00:00:03,815
MCP简单来说就是AI大模型的标准化工具箱。

2
00:00:03,899 --> 00:00:09,896
在我们之前的很多期视频里面，
只是讨论了如何使用别人已经编写好的MCP Server，

3
00:00:09,978 --> 00:00:13,435
对接到AI客户端里面，实现各种智能体育工作流。

4
00:00:13,519 --> 00:00:16,615
本期视频我们转换到开发者的角度，超简单，

5
00:00:16,699 --> 00:00:20,516
几分钟之内编写一个 MCP Server，
并且把它发布上线，

6
00:00:20,600 --> 00:00:22,115
提供给所有人去使用。

7
00:00:22,199 --> 00:00:29,199
本期视频内容较多并且十分硬核，
可以帮助我们从开发者的角度更加深入理解 MCP 协议。

8
00:00:30,899 --> 00:00:35,375
在爬爬虾的这期视频里面对 MCP
协议有过详细介绍了，

9
00:00:35,459 --> 00:00:36,899
这里我们简单总结一下。

10
00:00:37,000 --> 00:00:42,055
MCP 全称是模型上下文协议， MCP
服务或者叫做 MCP Server。

11
00:00:42,139 --> 00:00:47,100
作为AI与外部工具的中间层，
代替人类访问并且操作外部工具。

12
00:00:47,259 --> 00:00:51,396
MCP Server本质上就是一段Node
JS或者Python程序。

13
00:00:51,478 --> 00:00:58,478
大模型通过操作系统的STDIO，
也就是标准输入输出通道或者SSE协议调用某个 MCP Server。

14
00:01:00,098 --> 00:01:01,899
消息格式是类似这种的。

15
00:01:02,039 --> 00:01:04,495
MCP Server接收到这些消息以后，

16
00:01:04,578 --> 00:01:10,019
通过自己的代码功能或者使用API请求访问外部工具并
且完成任务。

17
00:01:10,118 --> 00:01:14,135
MCP
Server本质上就是一段Python或者Node JS程序。

18
00:01:14,218 --> 00:01:17,498
编写一个 MCP Server，也就是编写这段程序。


19
00:01:17,618 --> 00:01:20,715
我们来到 MCP 在GitHub上面的官方仓库，

20
00:01:20,799 --> 00:01:21,739
就是这个地址。

21
00:01:21,879 --> 00:01:27,278
这里有两个SDK，Python，
还有Typescript。SDK也就是开发工具包。

22
00:01:27,459 --> 00:01:30,355
本期视频我们主要以Python的SDK为例，

23
00:01:30,438 --> 00:01:32,596
演示一下创建一个 MCP Server。

24
00:01:32,679 --> 00:01:35,855
我们点击Python SDK，在安装这一部分，

25
00:01:35,938 --> 00:01:38,936
MCP 推荐使用UV去管理Python项目，

26
00:01:39,019 --> 00:01:43,596
我们点击这个链接。
UV是目前热度最高的Python环境管理工具。

27
00:01:43,679 --> 00:01:46,599
我们准备工作的第一步就是把UV安装一下。

28
00:01:46,698 --> 00:01:51,016
在UV的官网点击installation，
找到对应系统的安装命令。

29
00:01:51,099 --> 00:01:53,855
我是Windows系统的我就执行这个命令，

30
00:01:53,938 --> 00:01:55,355
我把这个命令复制一下。

31
00:01:55,438 --> 00:01:57,635
注意这是一个PowerShell的命令，

32
00:01:57,718 --> 00:02:00,338
在桌面右键，在终端打开。

33
00:02:00,459 --> 00:02:03,036
我们打开一个PowerShell的命令行窗口，

34
00:02:03,118 --> 00:02:07,275
把命令粘贴进来，回车执行。UV安装成功以后，

35
00:02:07,358 --> 00:02:10,639
我们可以来查看一下电脑上已经安装过的
Python版本。

36
00:02:10,758 --> 00:02:12,996
输入这个命令UV python --list，

37
00:02:13,079 --> 00:02:15,355
这里列出的是电脑上的 Python版本，

38
00:02:15,438 --> 00:02:16,475
还有它的安装路径。

39
00:02:16,558 --> 00:02:19,235
我现在想把这个三点一三的版本安装一下，

40
00:02:19,318 --> 00:02:23,415
执行这个命令。UV python --
install后面接版本号三点一三，

41
00:02:23,498 --> 00:02:24,329
回车。

42
00:02:24,699 --> 00:02:27,496
我新建了一个文件夹，叫MCP Server，

43
00:02:27,579 --> 00:02:30,739
我准备在这里面创建我们第一个 MCP Server。


44
00:02:30,878 --> 00:02:35,675
我在PowerShell这边使用CD命令先切换到我
们刚才创建的文件夹，

45
00:02:35,758 --> 00:02:42,156
然后执行UV init点，
就是把当前这个文件夹初始化成一个 Python 工程，

46
00:02:42,239 --> 00:02:45,075
后面接python版本号-GP三点一三，

47
00:02:45,158 --> 00:02:45,989
回车。

48
00:02:45,579 --> 00:02:48,455
接下来我们把MCP的SDK安装一下。

49
00:02:48,538 --> 00:02:51,596
执行这个命令uv add，然后一对引号，

50
00:02:51,679 --> 00:02:54,739
里面是MCP，方括号里面CLI。

51
00:02:54,979 --> 00:02:59,816
这个命令就是把 MCP 的SDK，
也就是开发工具包安装上，

52
00:02:59,899 --> 00:03:04,216
回车。
接下来我们找一个IDE打开我们刚才新创建的文件夹，

53
00:03:04,299 --> 00:03:05,618
这里我使用VS code。

54
00:03:05,778 --> 00:03:09,598
爬爬虾之前有一期完整视频介绍关于 VS
code的使用。

55
00:03:09,699 --> 00:03:11,836
使用VS code编写 Python程序，

56
00:03:11,919 --> 00:03:15,415
我们需要在左侧的Extension里面搜索Pyth
on，

57
00:03:15,498 --> 00:03:19,116
然后把Python
debugger跟Python这两个插件安装一下。

58
00:03:19,199 --> 00:03:22,376
接下来我们使用VS code打开刚才创建的文件夹，

59
00:03:22,459 --> 00:03:24,316
就是这个 MCP Server的文件夹。

60
00:03:24,399 --> 00:03:27,415
我们看到UV已经帮我们配置好了开发环境。

61
00:03:27,498 --> 00:03:30,415
点venv文件夹是 Python的虚拟环境，

62
00:03:30,498 --> 00:03:33,596
py project文件定义了项目的基本信息，

63
00:03:33,679 --> 00:03:35,818
main文件是一个基础的代码样例。

64
00:03:35,979 --> 00:03:38,316
我们可以在右上角点击这个run按钮，

65
00:03:38,399 --> 00:03:41,919
成功打印了结果，我们的
Python环境就配置完成了。

66
00:03:42,038 --> 00:03:44,118
接下来开始编写 MCP Server。

67
00:03:46,139 --> 00:03:48,536
然后我们来到 Python SDK这边，

68
00:03:48,618 --> 00:03:52,336
前面几步已经完成了，下面需要做的是把代码拷贝进来，

69
00:03:52,419 --> 00:03:53,656
我把代码复制一下。

70
00:03:53,739 --> 00:03:58,336
前两行的写法是固定的，在 MCP 包里面导入
fast MCP，

71
00:03:58,419 --> 00:04:01,538
然后使用 fast MCP初始化 MCP对象。

72
00:04:01,639 --> 00:04:04,156
接下来这一句是 python 里面的装饰器，

73
00:04:04,239 --> 00:04:06,479
用来给下面这个函数增加功能。

74
00:04:06,618 --> 00:04:10,675
在这里表示声明下面这个函数是一个 MCP 工具，

75
00:04:10,758 --> 00:04:13,876
这里有几个关键点，这个注释是必须要写的，

76
00:04:13,959 --> 00:04:19,538
这个注释的意思是使用自然语言告诉AI大模型这个函数
的功能是什么。

77
00:04:19,658 --> 00:04:24,915
在这个例子里面就是把两个数字加起来。
AI大模型拿到这个 MCP Server以后，

78
00:04:24,999 --> 00:04:29,439
它就知道我有一个工具，
这个工具可以帮助我精确地计算加法。

79
00:04:29,538 --> 00:04:34,836
在这个例子里面代码非常的简单，
当然我们也可以把一些很复杂的功能放到这里面，

80
00:04:34,918 --> 00:04:36,538
形成一些很有用的工具。

81
00:04:36,718 --> 00:04:41,935
第二个关键点是这里的类型修饰符，
在这个函数里面接收两个int变量，

82
00:04:42,019 --> 00:04:43,435
然后返回一个int值。

83
00:04:43,519 --> 00:04:49,755
这个类型修饰符是一定要写的，
它有助于帮助大模型理解工具的传参是什么类型的，

84
00:04:49,838 --> 00:04:53,079
可以帮助大模型更精准地调用 MCP 工具。

85
00:04:53,178 --> 00:04:57,636
我们看到修饰符有两种， mcp.tool，还有
mcp.resource。

86
00:04:57,718 --> 00:05:00,336
mcp.tool声明了这是一个工具，

87
00:05:00,418 --> 00:05:04,255
类似于HTTP REST API里面的 post
方法。

88
00:05:04,338 --> 00:05:07,216
大模型使用工具与外部系统进行交互，

89
00:05:07,298 --> 00:05:10,615
通常会产生副作用，比如写入文件、发送邮件，

90
00:05:10,699 --> 00:05:11,858
还有写数据库等等。

91
00:05:12,019 --> 00:05:15,615
下面的resource类似于HTTP里面的 get
方法，

92
00:05:15,699 --> 00:05:20,555
通常resource为大模型提供只读数据，
resource只是请求数据，

93
00:05:20,639 --> 00:05:24,776
但不会产生任何副作用，有点像REST API里面的
get 方法。

94
00:05:24,858 --> 00:05:26,978
一个 MCP Server的代码就这么多。

95
00:05:27,119 --> 00:05:30,875
最后我们再加两句话，
让它可以变成一个可以独立运行的程序，

96
00:05:30,959 --> 00:05:34,675
我们就加上这一句 MCP.run，括号里面是协议。

97
00:05:34,759 --> 00:05:37,755
这里我们先使用最简单的STDIO协议，

98
00:05:37,838 --> 00:05:41,255
我们找一个AI客户端来试一下刚才编写好的程序。

99
00:05:41,338 --> 00:05:43,959
这里我使用的AI客户端是Cherry Studio。


100
00:05:44,119 --> 00:05:49,156
爬爬虾之前有一期完整视频介绍使用 Cherry
Studio接入 MCP Server。

101
00:05:49,238 --> 00:05:51,838
我们先进入设置，找到 MCP 服务器。

102
00:05:52,019 --> 00:05:56,538
右上角如果有一个红色的叹号，
我们需要先把UV跟Burn安装一下。

103
00:05:56,738 --> 00:05:59,735
Cherry
Studio使用的是它内置的UV还有Burn。

104
00:05:59,819 --> 00:06:01,156
安装完成，我们继续。

105
00:06:01,238 --> 00:06:04,396
先退出来。这里的协议选择STDIO。

106
00:06:04,478 --> 00:06:08,915
注意命令这里写UV，不要写错了，
下面的参数都是固定的，

107
00:06:08,999 --> 00:06:11,596
我们这么写。先写两个横杠directory。

108
00:06:11,678 --> 00:06:14,836
第二行我们来到项目这里，把项目地址复制一下，

109
00:06:14,918 --> 00:06:16,276
这里一个斜线就够了。

110
00:06:16,358 --> 00:06:21,336
第三行run，第四行写main.py，
就是我们代码文件的名字，

111
00:06:21,418 --> 00:06:25,016
填好以后点击保存，这里打上一个绿灯就配置成功了。

112
00:06:25,098 --> 00:06:25,928
我们来试一下。

113
00:06:25,918 --> 00:06:29,755
我们选择一个 AI模型，这里我用的Gemini 2.
5 Flash。

114
00:06:29,838 --> 00:06:33,935
爬爬虾之前有一期视频介绍把 Gemini接入
Cherry Studio，

115
00:06:34,019 --> 00:06:37,156
在 MCP 服务器这里找到我们刚才添加的那个，

116
00:06:37,238 --> 00:06:38,519
我们来试一下它的加法。

117
00:06:38,639 --> 00:06:41,875
这里看到它调用了 MCP，正确地传递了参数，

118
00:06:41,959 --> 00:06:44,158
并且拿到了结果，最后完成了输出。

119
00:06:44,738 --> 00:06:47,495
除了使用 Cherry Studio，
接下来我们换一个AI客户端，

120
00:06:47,579 --> 00:06:49,016
这里我准备使用 cursor。

121
00:06:49,098 --> 00:06:52,675
我们先打开 cursor，找到右上角的设置 MCP，


122
00:06:52,759 --> 00:06:55,875
点击添加。cursor这边需要编写JSON文件，

123
00:06:55,959 --> 00:06:59,216
我们来到 Cherry Studio，找到设置还是
MCP，

124
00:06:59,298 --> 00:07:02,995
找到这个编辑按钮，我们把这个配置文件整个复制出来，

125
00:07:03,079 --> 00:07:05,396
然后粘贴到 cursor里面，保存一下。

126
00:07:05,478 --> 00:07:07,516
这里打上一个绿灯就配置成功了。

127
00:07:07,598 --> 00:07:11,355
我们来测一下，还是两个数相加，它准备调用 MCP
工具，

128
00:07:11,439 --> 00:07:15,415
我们点击运行，最终通过 MCP
的调用成功给到了结果。

129
00:07:15,499 --> 00:07:18,875
编写一个 STDIO 协议的 MCP Server，


130
00:07:18,959 --> 00:07:23,815
并且本地调用这部分的内容就完成了。

131
00:07:23,899 --> 00:07:29,790
接下来我们进入第二部分，使用SSE协议。
接下来我们把这个 MCP Server成 SSE 协议的。

132
00:07:29,891 --> 00:07:36,670
改成 SSE 协议非常的简单，
只需要在代码里的最后一行把 STDIO 改成 SSE。

133
00:07:36,812 --> 00:07:40,430
改成 SSE 我们需要服务端这边把它启动起来。

134
00:07:40,550 --> 00:07:47,307
右上角点击这个启动按钮，然后我们的 AI
客户端使用 SSE 协议远程调用这个 MCP Server，

135
00:07:47,391 --> 00:07:48,327
这样完成功能。

136
00:07:48,411 --> 00:07:51,307
我们启动起来以后，把这个监听的地址复制一下，

137
00:07:51,391 --> 00:07:53,108
回到 Cherry Studio 的设置，

138
00:07:53,192 --> 00:07:57,987
找到我们的 MCP，这次把类型从 STDIO 改成
SSE。

139
00:07:58,071 --> 00:08:02,288
接下来填写一个 URL，我们填写刚才复制的
Server 端的地址，

140
00:08:02,370 --> 00:08:04,108
后面加斜线 SSE。

141
00:08:04,190 --> 00:08:05,927
好，这样完成我们保存一下。

142
00:08:06,011 --> 00:08:07,990
回到聊天界面，我们再试一下。

143
00:08:08,211 --> 00:08:11,987
在下面把 MCP 服务器选中，询问 AI 一个问题，


144
00:08:12,071 --> 00:08:15,127
它成功调用了 MCP Server，并且给出了结果。


145
00:08:15,211 --> 00:08:18,788
我们回到 VSCode
这边可以看到这些远程的调用记录，

146
00:08:18,870 --> 00:08:25,747
这样证明了实际的代码执行是在 VSCode 这边。
AI客户端通过 SSE 协议远程调用了这个方法，

147
00:08:25,831 --> 00:08:26,848
并且拿到了结果。

148
00:08:26,930 --> 00:08:33,931
总结一下 SSE 与 STDIO 的区别。
STDIO 协议一般是客户端把 MCP Server，

149
00:08:34,071 --> 00:08:36,668
也就是这个 Python 程序下载到本机，

150
00:08:36,750 --> 00:08:41,668
并且本机运行。AI客户端与 MCP Server
使用 STDIO，

151
00:08:41,750 --> 00:08:45,171
也就是操作系统的标准输入输出通道进行交互。

152
00:08:45,350 --> 00:08:52,350
使用 STDIO 协议， AI 客户端与 MCP
Server 的距离更近一些。SSE 协议则是把 MCP Server，

153
00:08:53,270 --> 00:09:00,270
也就是这个 Python 程序单独部署。
AI客户端与 MCP Server 使用 SSE 协议进行远程调用。

154
00:09:00,591 --> 00:09:05,311
使用 SSE 协议， AI 客户端与 MCP
Server 的距离更远一些。

155
00:09:05,490 --> 00:09:11,148
除了 SSE 协议， MCP Server
目前还支持另外一种是Streamable HTTP，

156
00:09:11,230 --> 00:09:14,451
这个跟 SSE 功能非常像，也是远程调用。

157
00:09:14,571 --> 00:09:18,607
我们把协议类型改成Streamable HTTP
启动起来，

158
00:09:18,691 --> 00:09:20,730
然后客户端也需要相应的改动。

159
00:09:20,971 --> 00:09:27,971
我们来到客户端，这里的类型选择Streamable
HTTP，URL 末尾从 SSE 改成 MCP，

160
00:09:28,451 --> 00:09:29,551
最后点击保存。

161
00:09:29,671 --> 00:09:32,390
这样同样的可以配置成功，我们再来试一下。

162
00:09:32,490 --> 00:09:35,227
同样的完成了调用，最终结果也是正确的。

163
00:09:35,311 --> 00:09:38,107
同样的我们可以把这套配置放到 Cursor 上面，

164
00:09:38,191 --> 00:09:40,248
我们来到 Cherry Studio 的设置，

165
00:09:40,331 --> 00:09:43,628
点击这个小按钮，然后把这个配置项复制下来，

166
00:09:43,711 --> 00:09:47,268
来到 Cursor 的设置，点击 Add New
Global MCP，

167
00:09:47,350 --> 00:09:49,067
然后把整个配置粘贴过来。

168
00:09:49,150 --> 00:09:53,347
Cursor 有一点不同的是，需要把这里的
base_url 改成 URL，

169
00:09:53,431 --> 00:09:54,260
然后保存。

170
00:09:54,150 --> 00:09:54,980
这样就成功了。

171
00:09:55,030 --> 00:09:55,971
我们来测一下。

172
00:09:57,451 --> 00:10:00,307
成功调用了 MCP Server 并且给到了输出。

173
00:10:00,390 --> 00:10:03,268
好，本期视频前半段的内容我们就完成了，

174
00:10:03,350 --> 00:10:06,807
我们介绍了如何在本地编写一个 MCP Server，


175
00:10:06,890 --> 00:10:09,410
并且在本地的 AI 客户端里面去使用。

176
00:10:09,571 --> 00:10:13,447
这里我们介绍了三种协议，STDIO， SSE，

177
00:10:13,530 --> 00:10:15,350
还有Streamable HTTP。

178
00:10:15,490 --> 00:10:19,288
下一个章节我们把这个 MCP
Server发布到公网上面，

179
00:10:19,370 --> 00:10:20,850
让所有人都可以使用。

180
00:10:22,230 --> 00:10:25,707
把一个 MCP Server
发布到公网上面有两种方式，

181
00:10:25,791 --> 00:10:28,567
最常见的就是把它打包成一个 Python 包，

182
00:10:28,650 --> 00:10:34,788
并且上传到 PyPI 上面，其他人使用 uvx
命令就可以自动地下载到这个 Python 包，

183
00:10:34,870 --> 00:10:36,171
并且本地执行。

184
00:10:36,311 --> 00:10:39,128
另外一种方式，我们就使用 SSE 协议，

185
00:10:39,211 --> 00:10:41,947
把我们的程序部署到一个云服务器上面，

186
00:10:42,030 --> 00:10:47,951
然后暴露出来一个公网链接，
其他人使用这个公网链接就可以使用我们的 MCP Server。

187
00:10:48,051 --> 00:10:50,971
这里我们先看第一种方法，上传到 PyPI 上面。

188
00:10:51,150 --> 00:10:55,168
这里我新建一个文件夹，叫做技术爬爬虾的 MCP
Demo，

189
00:10:55,250 --> 00:10:57,467
因为 PyPI 是没有命名空间的，

190
00:10:57,551 --> 00:11:01,288
所以我们的测试程序最好在前面加上自己的名字，

191
00:11:01,370 --> 00:11:02,788
以免跟别人混淆起来。

192
00:11:02,870 --> 00:11:07,927
我们在命令行切换到对应的文件夹，我们输入 uv
init点，

193
00:11:08,010 --> 00:11:10,650
把这个文件夹初始化一下，后面加两个-

194
00:11:10,831 --> 00:11:14,687
package表示初始化的时候把它初始化成一个
Python 包，

195
00:11:14,770 --> 00:11:18,307
然后指定 Python 版本-p3.13回车。

196
00:11:18,390 --> 00:11:24,350
接下来还是添加 MCP 的依赖，uv add 里面
MCP CLI 回车。

197
00:11:24,490 --> 00:11:27,288
好，我们再用 VSCode 打开这个文件夹，

198
00:11:27,370 --> 00:11:30,668
这次我们要把代码写到这个 init点py 里面，

199
00:11:30,750 --> 00:11:32,827
看到这里面已经有个 main 方法了，

200
00:11:32,910 --> 00:11:38,087
我们保留这个 main 方法，
接下来把我们刚才写的本地的那套代码粘贴在上面，

201
00:11:38,171 --> 00:11:43,908
然后把 MCP.run 写到 main 方法里面
这里的协议使用 STDIO，

202
00:11:43,990 --> 00:11:48,508
因为这是一个 Python 包，别人是把这个
Python 包下载到本地进行使用的，

203
00:11:48,591 --> 00:11:50,010
代码就编写完成了。

204
00:11:50,311 --> 00:11:52,211
我们执行这个命令打包试一下。

205
00:11:52,350 --> 00:11:54,607
uv build 看到打包成功了。

206
00:11:54,691 --> 00:11:58,128
接下来我们把这个打好了包发布到 PyPI 上面，

207
00:11:58,211 --> 00:11:59,850
提供给所有人下载使用。

208
00:12:00,071 --> 00:12:03,988
首先我们需要注册一下 PyPI，
在右上角点击register，

209
00:12:04,071 --> 00:12:08,148
填写用户名邮箱密码，这里我叫 Tech Trump
技术爬爬虾，

210
00:12:08,230 --> 00:12:11,888
右上角点击 login，然后我们需要收一下验证邮件，


211
00:12:11,971 --> 00:12:15,227
这里来到我的邮箱，点击验证链接完成验证。

212
00:12:15,311 --> 00:12:19,648
接下来点击生成恢复码，屏幕上一共会出现八组恢复码，

213
00:12:19,730 --> 00:12:20,847
我们把它保存下来。

214
00:12:20,931 --> 00:12:24,307
接下来我们把第一组恢复码填进去，点击verify。

215
00:12:24,390 --> 00:12:30,388
点击添加双重身份验证，
然后我们去手机的软件市场下载微软的 authenticator，

216
00:12:30,471 --> 00:12:33,427
点击上面的加号，然后选择个人账户，

217
00:12:33,510 --> 00:12:39,268
使用手机扫描电脑屏幕上的二维码，
然后把手机上生成的这个六位数的验证码填写过来，

218
00:12:39,350 --> 00:12:41,071
这样就完成了双重身份验证。

219
00:12:41,451 --> 00:12:43,268
在 Account Setting 这里往下找，

220
00:12:43,350 --> 00:12:45,668
有一个API Token，我们点击一下，

221
00:12:45,750 --> 00:12:50,087
随便填个名字，我叫技术爬爬虾，然后范围选择所有项目，


222
00:12:50,171 --> 00:12:54,327
点击生成，这样就生成了一个很长的 PyPI 的
API Token，

223
00:12:54,410 --> 00:12:56,067
我们把它复制下来保存好。

224
00:12:56,150 --> 00:13:01,087
有了Token以后，我们执行这个命令 uv
publish 两个横杠 token 空格，

225
00:13:01,171 --> 00:13:04,488
后面就是 PyPI 网站的 Token，
我们粘贴过来，

226
00:13:04,571 --> 00:13:06,530
这样回车，这样就发布成功了。

227
00:13:06,630 --> 00:13:07,870
我们来 PyPI 看一下。

228
00:13:08,010 --> 00:13:11,067
我们找到右上角自己的名字 Your
projects，

229
00:13:11,150 --> 00:13:13,748
这个就是我们刚刚上传的 MCP Server，

230
00:13:13,831 --> 00:13:17,288
我们点击 View，这就是一个功能齐全的
Python 包。

231
00:13:17,370 --> 00:13:19,288
当然为了正规，最好填一个项目描述，

232
00:13:19,370 --> 00:13:23,768
然后我们就可以在客户端里面使用这个 MCP
Server 了。

233
00:13:23,850 --> 00:13:28,168
我们把这个包的名字复制一下，回到 Cherry
Studio 这边添加服务器，

234
00:13:28,250 --> 00:13:33,971
快速创建类型，选择 STDIO 命令填写 uvx
一定不要搞错了。

235
00:13:34,150 --> 00:13:37,668
下面的参数就把我们自己上传的那个包名填写过来，

236
00:13:37,750 --> 00:13:38,687
最后点击保存。

237
00:13:38,770 --> 00:13:40,668
这里打上一个绿灯就没问题了。

238
00:13:40,750 --> 00:13:43,567
我们来测一下，看到成功调用了 MCP 服务，

239
00:13:43,650 --> 00:13:44,768
并且输出了结果。

240
00:13:44,850 --> 00:13:49,791
在这里点击编辑，
我们可以把这个配置文件发送给任何一个小伙伴们去使用。

241
00:13:50,191 --> 00:13:55,288
这是我们自己发布到公网上的 MCP Server，

242
00:13:55,370 --> 00:13:56,201
其他人通过这个名字就可以下载下来并且本地使用，

243
00:13:55,390 --> 00:14:02,390
效果非常的棒，感觉很好。
我们来看MCP的另外一种发布方式，

244
00:14:03,438 --> 00:14:08,296
在公网上暴露一个SSE的地址，
然后让其他人进行远程调用。

245
00:14:08,379 --> 00:14:13,317
我们只需要把写好的Python程序部署到一个公网
IP 的云服务器上面就可以了。

246
00:14:13,399 --> 00:14:15,897
这是我的一台Ubuntu系统的云服务器。

247
00:14:15,980 --> 00:14:19,135
我们先把UV安装一下，这里来到UV的官网，

248
00:14:19,219 --> 00:14:24,135
找到Linux系统的安装命令，复制进来执行一下。
UV安装完成以后，

249
00:14:24,219 --> 00:14:26,019
记得重开一下命令行窗口。

250
00:14:26,119 --> 00:14:28,937
首先创建一个文件夹叫MCP Demo。

251
00:14:29,019 --> 00:14:32,655
然后我们进入到这个文件夹，再来打开FTP工具，

252
00:14:32,739 --> 00:14:35,556
我们找到创建好的文件夹，把代码拷贝进来。

253
00:14:35,639 --> 00:14:38,015
这是我们本期视频创建的第一个项目。

254
00:14:38,099 --> 00:14:40,636
注意，这个不是打包那个，这里需要改一下，

255
00:14:40,720 --> 00:14:44,556
首先要把协议改成SSE，接下来再添加一句话，

256
00:14:44,639 --> 00:14:49,176
把Host设成四个零，
这样可以让我们的Server从远程被访问到。

257
00:14:49,259 --> 00:14:53,515
接下来我们把这个代码main点PY直接拖到服务器对
应的目录下面，

258
00:14:53,599 --> 00:14:56,216
还需要把pyproject文件也拖进来。

259
00:14:56,298 --> 00:14:58,535
代码准备好以后，我们使用UV来启动，

260
00:14:58,619 --> 00:15:02,535
首先配置虚拟环境UV VENV回车。

261
00:15:02,619 --> 00:15:06,135
第二步安装依赖UV pip install点，

262
00:15:06,219 --> 00:15:08,517
就是安装当前目录这个项目的依赖。

263
00:15:08,600 --> 00:15:12,318
下一个命令就可以启动了。UV run
main点PY。

264
00:15:12,979 --> 00:15:16,916
我们还是来到Cherry
Studio来编辑一下这个 MCP 服务器，

265
00:15:16,999 --> 00:15:21,895
类型选择SSE，URL 这里填HTTP冒号两个斜线，


266
00:15:21,979 --> 00:15:24,535
接下来填写服务器的公网 IP 地址，

267
00:15:24,619 --> 00:15:27,956
端口八千斜线SSE，然后保存一下。

268
00:15:28,038 --> 00:15:31,355
我们在聊天窗口把MCP服务器开启起来，

269
00:15:31,438 --> 00:15:34,676
询问它。成功调用了服务器上面的 MCP
Server，

270
00:15:34,759 --> 00:15:35,875
并且给出了结果。

271
00:15:35,958 --> 00:15:39,797
这就是怎么把一个 MCP 服务器放到服务器上面运行，


272
00:15:39,879 --> 00:15:43,340
并且通过 SSE 协议为 AI 客户端提供服务。

273
00:15:45,919 --> 00:15:51,076
总结一下，本期视频我们使用Python SDK
从零到一创建 MCP 工具，

274
00:15:51,158 --> 00:15:56,336
总共介绍了四种场景，本地使用STUDIO，本地使用
SSE，

275
00:15:56,418 --> 00:15:59,299
还有发布STUDIO，发布SSE。

276
00:15:59,399 --> 00:16:02,635
Node JS 那边原理跟Python是一模一样的，


277
00:16:02,719 --> 00:16:04,339
只是换了一种编程语言。

278
00:16:04,438 --> 00:16:07,456
本期视频篇幅有限，我们就不展开讲了，

279
00:16:07,538 --> 00:16:10,178
感谢大家点赞支持，我们下期再见。
