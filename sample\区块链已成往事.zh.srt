1
00:00:00,119 --> 00:00:03,875
有这么一个技术热潮，它席卷全球，让全民狂热，

2
00:00:03,959 --> 00:00:08,159
人人参与。资本为它孤注一掷，投入大量的定制硬件。

3
00:00:08,759 --> 00:00:13,359
它号称是革命性的技术突破，它誓要颠覆现有的商业秩序。


4
00:00:14,359 --> 00:00:18,638
我说的不是现在的 AI，而是十年前比 AI
还火的区块链。

5
00:00:19,719 --> 00:00:24,876
当年这矿我挖过，这币我炒过，技术我研究过，

6
00:00:24,958 --> 00:00:26,118
项目我开发过。

7
00:00:26,739 --> 00:00:30,736
作为曾经的重度参与者，看着如今死翘翘的区块链，

8
00:00:30,818 --> 00:00:34,600
我却丝毫不怀念它，甚至想说一句好死。

9
00:00:36,959 --> 00:00:41,299
所有算得上是成功的区块链应用都没能跳出电子货币的范
畴。

10
00:00:41,918 --> 00:00:44,496
电子货币是区块链最完美的应用场景，

11
00:00:44,579 --> 00:00:48,896
就是因为去中心化，这个中本聪填上的最后一块拼图，

12
00:00:48,978 --> 00:00:51,960
自始至终都是区块链技术最重要的价值体现。

13
00:00:52,738 --> 00:00:58,097
那些加密货币和它们的衍生品，
比如可以上市赛博郁金香的 NFT，

14
00:00:58,179 --> 00:01:02,195
它们需要脱离现有政府、法律和社会秩序的约束，

15
00:01:02,279 --> 00:01:03,759
只有去中心化能做到。

16
00:01:04,638 --> 00:01:08,280
而国家发行的货币想要电子化，也需要去中心化。

17
00:01:08,760 --> 00:01:12,736
这样其他国家的政府和金融机构接受起来才更容易，

18
00:01:12,819 --> 00:01:14,780
货币才能在国际间流通起来。

19
00:01:15,379 --> 00:01:18,319
这也是数字人民币使用区块链技术的原因之一。

20
00:01:19,040 --> 00:01:21,417
可以说，在货币交易这个业务场景下，

21
00:01:21,500 --> 00:01:25,519
区块链做到了真正的技术突破，
解决了其他技术无法解决的需求。

22
00:01:26,418 --> 00:01:30,259
但不幸的是，电子货币也是区块链唯一有价值的应用场景。


23
00:01:30,819 --> 00:01:33,819
因为去中心化这个技术突破不具有通用价值。

24
00:01:34,180 --> 00:01:39,137
我们中的绝大多数既不是央行、
美联储这些掌握国家货币的机构，

25
00:01:39,219 --> 00:01:42,759
也不是需要跨国贩毒走私精火、洗黑钱的神秘大佬。

26
00:01:43,198 --> 00:01:46,637
我们日常面对的绝大多数问题，需要的不是去中心化，

27
00:01:46,719 --> 00:01:50,478
而是更传统的效率、容错率、灵活度等等。

28
00:01:50,879 --> 00:01:55,036
为了最大程度的去中心化而搞出来的 Proof of
work这类共识机制，

29
00:01:55,118 --> 00:01:56,759
反而会让企业望而却步。

30
00:01:57,118 --> 00:02:01,936
很多人对Proof of
work的风险认知仅停留在百分之五十的算力上，

31
00:02:02,019 --> 00:02:06,896
但实际上考虑到人都是自私贪婪的，在博弈论的影响下，

32
00:02:06,978 --> 00:02:08,859
真正的临界值是百分之三十三。

33
00:02:09,538 --> 00:02:15,039
用比特币举例，早在 2018年，
最大的挖矿团队就已经拥有了百分之二十的算力。

34
00:02:15,598 --> 00:02:20,359
如果头部的两个挖矿团队联合起来，
就已经可以操控比特币的共识了。

35
00:02:22,699 --> 00:02:25,620
不可控风险永远是企业需要优先规避的。

36
00:02:25,838 --> 00:02:30,056
所以业界在摸索区块链落地时，一般都不考虑公共链，

37
00:02:30,139 --> 00:02:31,218
而是联盟链。

38
00:02:31,659 --> 00:02:33,639
两者最大的区别就是准入门槛。

39
00:02:34,239 --> 00:02:41,019
联盟链不对公众公开，
加入网络的节点和参与数据读写的用户都需要获得授权批准。

40
00:02:41,459 --> 00:02:44,699
有了这个门槛，共识机制的设计空间就大了很多。

41
00:02:45,360 --> 00:02:47,656
比如 15年成立的Hyperledger项目，

42
00:02:47,739 --> 00:02:49,859
由 IBM、Intel 等巨头联手。

43
00:02:50,360 --> 00:02:57,360
他们的开源框架Fabric就是市面上最好的联盟链框
架。Fabric在一七年发布的一点零版本共识机制用的是 Kafka，

44
00:02:58,758 --> 00:03:05,217
大家熟悉的消息队列框架，此时的 Kafka
甚至还没有达成Exactly-once delivery的保障，

45
00:03:05,300 --> 00:03:08,819
所以是名副其实的别管准不准，你就说它快不快了。

46
00:03:09,500 --> 00:03:16,500
在二零年 Fabric 发布二点零版本时，
Kafka 被拿掉换成了分布式数据库常用的Raft协议，Raft 算法主要解决的是个别节点出现故障时如何达成共识的问题。

47
00:03:21,179 --> 00:03:23,258
这时速度就向容错让步了。

48
00:03:23,860 --> 00:03:27,795
等到了二四年的三点零版本 Raft 被换成了
BFT，

49
00:03:27,878 --> 00:03:30,199
与Raft相比，BFT 考虑的更多。

50
00:03:30,580 --> 00:03:34,816
除了节点出故障延迟之外，它还会考虑节点被黑客劫持、

51
00:03:34,899 --> 00:03:35,938
故意捣乱的情况。

52
00:03:36,500 --> 00:03:40,096
这个演化过程很好地体现出Fabric框架一直在灵活
度、

53
00:03:40,179 --> 00:03:43,137
容错率、效率等多个维度上进行调整，

54
00:03:43,219 --> 00:03:46,399
以求找到最合适的比例，满足业界的需求。

55
00:03:46,838 --> 00:03:50,060
但无奈的是，不管怎么找，都找不到那个平衡点。

56
00:03:52,799 --> 00:03:54,319
它的核心问题之一是复杂度。

57
00:03:55,019 --> 00:03:58,395
就拿Fabric框架举例，要搭建一个Hello
World系统，

58
00:03:58,479 --> 00:04:03,056
你至少要部署二十多个服务，其中包括负责签发证书的
CA，

59
00:04:03,139 --> 00:04:06,776
负责数据传导的共识框架，记录当前状态的数据库，

60
00:04:06,860 --> 00:04:08,599
以及参与读写的对等节点。

61
00:04:09,538 --> 00:04:12,175
我记得我当初照着官方文档启动一个Hello
World，

62
00:04:12,258 --> 00:04:16,499
花了差不多三天才成功，
因为这里面有太多可以出错的地方了。

63
00:04:17,298 --> 00:04:21,276
更糟糕的是，因为区块链包含了大量的新名词和新流程，

64
00:04:21,358 --> 00:04:23,759
如果你不全部了解，就会寸步难行。

65
00:04:24,199 --> 00:04:27,115
所以不管是负责设计的、开发的、测试的、

66
00:04:27,199 --> 00:04:33,079
维护的，还是最后的运营和用户，
所有的参与方都得先成为区块链专家才能进场。

67
00:04:33,699 --> 00:04:35,999
这也是很多区块链项目半途而废的原因。

68
00:04:36,358 --> 00:04:39,656
立项之初觉得一帆风顺，直到多方入场之后，

69
00:04:39,738 --> 00:04:42,059
才发现门槛比想象中高太多了。

70
00:04:42,439 --> 00:04:45,199
算了算有点不划算，所以最后搁置下来。

71
00:04:45,939 --> 00:04:49,259
但比起复杂度，区块链更大的问题在于运行效率。

72
00:04:50,040 --> 00:04:55,716
我们很容易就可以得出一个结论，
区块链从根本上不可能比经典架构更快，

73
00:04:55,798 --> 00:04:57,278
因为默认一切不可信。

74
00:04:57,738 --> 00:05:01,475
区块链必须花费更多的资源和流程去检查节点，

75
00:05:01,559 --> 00:05:02,639
验收数据的真伪。

76
00:05:03,459 --> 00:05:07,255
不管你用的是哪种共识算法，都需要大量的双向通讯，

77
00:05:07,338 --> 00:05:08,459
大量的数据往返。

78
00:05:08,939 --> 00:05:12,999
而且节点越来越多之后，达成共识的成本也会越来越高。

79
00:05:13,879 --> 00:05:16,216
经典架构则完全没有这些烦恼。

80
00:05:16,298 --> 00:05:20,175
除了Master节点挂掉，
要另选Master之类的特殊情况，

81
00:05:20,259 --> 00:05:23,995
绝大部分的数据通讯都是从Master到Slave的
单向通讯，

82
00:05:24,079 --> 00:05:27,776
简单高效，由此也带来了极强的扩容能力，

83
00:05:27,858 --> 00:05:30,178
节点的增加对通讯成本几乎没有影响。

84
00:05:31,379 --> 00:05:36,896
当年促使我弃坑区块链的导火索，
就是我用区块链技术开发了一个物联网系统之后，

85
00:05:36,978 --> 00:05:38,119
发现它慢到离谱。

86
00:05:38,678 --> 00:05:41,096
我用当时市面上最顶配的电脑去跑测试，

87
00:05:41,178 --> 00:05:47,658
甚至都跑不到十个 TPS，
我都不敢想把它部署在服务器上会是什么一个景象。

88
00:05:47,939 --> 00:05:51,737
更多比我聪明的人则是在试机阶段就发现了这个问题。

89
00:05:51,819 --> 00:05:57,536
比如数字人民币就在白皮书上提到，
区块链的效率无法应对正常的交易需求，

90
00:05:57,619 --> 00:06:01,435
所以在架构设计上，
数字人民币明确地分开了发行层和交易层。

91
00:06:01,519 --> 00:06:07,142
所以-
只有在频率比较低的货币发行机构对账上用到了区块链。

92
00:06:07,583 --> 00:06:11,220
而在日常交易中，用的还是经典的中心化架构，

93
00:06:11,302 --> 00:06:13,163
一切交易走央行的服务器。

94
00:06:13,923 --> 00:06:17,762
因为高频高并发的交易场景，只有经典架构能够应付。

95
00:06:20,144 --> 00:06:24,521
在区块链热潮最顶峰的时候，整个业界都是在自我麻醉的，


96
00:06:24,603 --> 00:06:28,339
他们没有直面区块链的技术缺陷，而是给它贴金，

97
00:06:28,423 --> 00:06:29,403
强行上价值。

98
00:06:29,862 --> 00:06:32,560
举一个很经典的例子，在二零一八年，

99
00:06:32,642 --> 00:06:39,403
新加坡能源，
也就是新加坡管天力的国企推出了世界上第一个基于区块链的绿色证书交易所。

100
00:06:39,882 --> 00:06:43,343
绿色证书 IEC 就是类似于碳排放权的东西。

101
00:06:43,843 --> 00:06:47,420
包括新加坡在内的东南亚国家都有环保类的交易所，

102
00:06:47,502 --> 00:06:49,322
也都可以进行 IEC 的交易。

103
00:06:49,923 --> 00:06:52,939
那么为什么还要用区块链造一个新的交易所呢？

104
00:06:53,023 --> 00:06:57,600
对此，新加坡能源公司的描述是，
它能够让交易变得更简单、

105
00:06:57,682 --> 00:06:58,802
安全、高效。

106
00:06:59,543 --> 00:07:00,903
这真的是哄堂大笑。

107
00:07:01,822 --> 00:07:06,043
所以毫无意外地，在发布会之后，
这个交易所就再也没有被提及过了。

108
00:07:06,622 --> 00:07:10,081
新加坡能源公司甚至都没有发一个下线公告，

109
00:07:10,163 --> 00:07:12,142
估计是觉得这次跟风有点丢人吧。

110
00:07:13,163 --> 00:07:16,261
这个例子很经典，是因为它提到的这几个卖点，

111
00:07:16,343 --> 00:07:19,502
也是很多企业在搞区块链落地时候用到的说法。

112
00:07:20,423 --> 00:07:25,822
但别说简单和高效了，就连安全，
区块链也没有比经典架构好到哪里去。

113
00:07:26,343 --> 00:07:29,261
因为大家用的都是同一个套路的加密算法，

114
00:07:29,343 --> 00:07:32,983
都是用私钥对数据签名，然后用公钥核实签名。

115
00:07:33,603 --> 00:07:37,160
加密算法安全，大家就都安全，加密算法被破解了，

116
00:07:37,242 --> 00:07:38,523
大家也都得一起死。

117
00:07:39,583 --> 00:07:43,300
这种情况下，甚至可以说经典架构还更有一丝生机，

118
00:07:43,382 --> 00:07:45,523
因为你还有机会打补丁，换算法。

119
00:07:46,062 --> 00:07:51,202
而区块链因为 Inmutable
的核心机制是没有办法去改内部的加密算法的。

120
00:07:53,843 --> 00:07:58,879
在热潮褪去之后，大家才意识到，
那些现有技术解决不了的问题，

121
00:07:58,963 --> 00:08:00,202
区块链也解决不了。

122
00:08:00,642 --> 00:08:05,262
而那些能用现有技术解决的问题，
区块链也没办法提供更好的解法。

123
00:08:06,124 --> 00:08:11,142
对于绝大多数的政府、企业和个人，
区块链就是这么鸡肋的一个东西。

124
00:08:12,182 --> 00:08:15,201
如果说大数据浪潮留下来了丰厚的技术遗产、

125
00:08:15,283 --> 00:08:20,103
先进的商业理念，那么相比起来，
区块链留下的是一地鸡毛。

126
00:08:20,802 --> 00:08:25,142
这项技术唯一存活的分支成了各路骗子和傻子的舞台。

127
00:08:25,783 --> 00:08:29,660
割韭菜的用来骗韭菜，创业者用来骗投资人，

128
00:08:29,742 --> 00:08:31,002
总统用来骗民众。

129
00:08:31,983 --> 00:08:35,643
这对于那些为这项技术奠基的计算机科学、

130
00:08:36,023 --> 00:08:39,400
网络安全、密码学领域的伟人们来说，

131
00:08:39,484 --> 00:08:40,943
真是一个莫大的讽刺。

132
00:08:41,682 --> 00:08:45,341
尤其是已逝世的 Len Sassaman 和
Hal Finney，

133
00:08:45,423 --> 00:08:49,583
两位理想主义者，也是圈内认为最有可能的中本村本人。

134
00:08:50,243 --> 00:08:53,919
他们要是知道自己创造的乌托邦会是这般结局，

135
00:08:54,003 --> 00:08:56,480
会不会后悔当初把它造出来呢？

136
00:08:56,563 --> 00:08:59,341
会不会宁愿让它跟自己一起长埋土里呢？

137
00:08:59,423 --> 00:09:01,342
我也只能 RIP 了。
