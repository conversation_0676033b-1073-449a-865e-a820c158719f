#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试韩语标点符号处理的修正效果
"""

import json
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.sentence_splitter import SentenceSplitter
from core.srt_processor import create_srt_from_json


def test_korean_punctuation_recognition():
    """测试韩语标点符号识别"""
    print("=== 测试韩语标点符号识别 ===")
    
    # 创建韩语句子分割器
    splitter = SentenceSplitter("kor")
    
    # 测试韩语常用标点符号
    korean_punctuation_tests = [
        ("입니다.", ".", 0),  # 拉丁语系句号，高优先级
        ("했습니다!", "!", 0),  # 拉丁语系感叹号，高优先级
        ("무엇인가?", "?", 0),  # 拉丁语系问号，高优先级
        ("그렇습니다,", ",", 2),  # 拉丁语系逗号，低优先级
        ("어,", ",", 2),  # 拉丁语系逗号，低优先级
        ("바랍니다:", ":", 1),  # 拉丁语系冒号，中优先级
        ("그리고;", ";", 1),  # 拉丁语系分号，中优先级
    ]
    
    for text, expected_punct, expected_priority in korean_punctuation_tests:
        word_info = {"text": text, "type": "word"}
        has_punct, punct, priority = splitter._word_ends_with_split_punct(word_info)
        
        print(f"文本: '{text}'")
        print(f"  预期标点: '{expected_punct}', 实际: '{punct}'")
        print(f"  预期优先级: {expected_priority}, 实际: {priority}")
        print(f"  识别结果: {'✓' if has_punct and punct == expected_punct and priority == expected_priority else '✗'}")
        print()


def test_korean_sentence_splitting():
    """测试韩语句子分割"""
    print("=== 测试韩语句子分割 ===")
    
    # 模拟韩语转录数据
    korean_words = [
        {"text": "다음", "type": "word", "start": 0.0, "end": 0.5},
        {"text": " ", "type": "spacing", "start": 0.5, "end": 0.6},
        {"text": "소식입니다.", "type": "word", "start": 0.6, "end": 1.5},
        {"text": " ", "type": "spacing", "start": 1.5, "end": 1.6},
        {"text": "문재인", "type": "word", "start": 1.6, "end": 2.0},
        {"text": " ", "type": "spacing", "start": 2.0, "end": 2.1},
        {"text": "대통령이", "type": "word", "start": 2.1, "end": 2.8},
        {"text": " ", "type": "spacing", "start": 2.8, "end": 2.9},
        {"text": "밝혔습니다.", "type": "word", "start": 2.9, "end": 4.0},
        {"text": " ", "type": "spacing", "start": 4.0, "end": 4.1},
        {"text": "권력형", "type": "word", "start": 4.1, "end": 4.8},
        {"text": " ", "type": "spacing", "start": 4.8, "end": 4.9},
        {"text": "부정부패의", "type": "word", "start": 4.9, "end": 5.8},
        {"text": " ", "type": "spacing", "start": 5.8, "end": 5.9},
        {"text": "척결은", "type": "word", "start": 5.9, "end": 6.5},
        {"text": " ", "type": "spacing", "start": 6.5, "end": 6.6},
        {"text": "피력했습니다.", "type": "word", "start": 6.6, "end": 8.0},
    ]
    
    splitter = SentenceSplitter("kor")
    sentence_groups = splitter.split_into_sentence_groups(korean_words)
    basic_entries = splitter.create_basic_subtitle_entries(sentence_groups)
    
    print(f"分割结果: {len(sentence_groups)} 个句子组")
    for i, group in enumerate(sentence_groups):
        text = "".join(w['text'] for w in group).strip()
        print(f"  句子 {i+1}: '{text}'")
    
    print(f"\n基本字幕条目: {len(basic_entries)} 个")
    for i, entry in enumerate(basic_entries):
        print(f"  条目 {i+1}: '{entry['text']}'")
        print(f"    时间: {entry['start']:.2f}s - {entry['end']:.2f}s")
    
    # 分析分割质量
    quality = splitter.analyze_split_quality(sentence_groups)
    print(f"\n分割质量分析:")
    print(f"  总组数: {quality['total_groups']}")
    print(f"  平均每组词数: {quality['avg_words_per_group']:.2f}")
    print(f"  标点结尾率: {quality['punct_ending_rate']:.2%}")


def test_korean_srt_generation():
    """测试韩语SRT生成"""
    print("=== 测试韩语SRT生成 ===")
    
    # 加载韩语样本文件 - 使用glob模式查找
    import glob
    sample_files = glob.glob("sample/*.kor.json")
    
    for sample_file in sample_files:
        if os.path.exists(sample_file):
            print(f"\n处理文件: {sample_file}")
            try:
                with open(sample_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                
                # 生成SRT
                srt_content = create_srt_from_json(json_data)
                
                # 分析前几个字幕条目
                srt_lines = srt_content.strip().split('\n\n')
                print(f"生成了 {len(srt_lines)} 个字幕条目")
                
                # 显示前3个条目
                for i, subtitle_block in enumerate(srt_lines[:3]):
                    lines = subtitle_block.strip().split('\n')
                    if len(lines) >= 3:
                        subtitle_text = '\n'.join(lines[2:])
                        print(f"  条目 {i+1}: '{subtitle_text}'")
                
                # 统计标点符号使用情况
                latin_punct_count = sum(srt_content.count(p) for p in ".,!?;:")
                cjk_punct_count = sum(srt_content.count(p) for p in "。，！？；：")
                
                print(f"  拉丁标点符号数量: {latin_punct_count}")
                print(f"  东亚标点符号数量: {cjk_punct_count}")
                print(f"  标点符号类型: {'主要使用拉丁标点' if latin_punct_count > cjk_punct_count else '主要使用东亚标点'}")
                
            except Exception as e:
                print(f"  处理失败: {e}")
        else:
            print(f"文件不存在: {sample_file}")


def main():
    """主函数"""
    print("韩语标点符号处理测试")
    print("=" * 50)
    
    test_korean_punctuation_recognition()
    test_korean_sentence_splitting()
    test_korean_srt_generation()
    
    print("\n测试完成!")


if __name__ == "__main__":
    main()
